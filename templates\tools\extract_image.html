{% extends "base.html" %}

{% block title %}Extract Images - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-images text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Extract Images from PDF</h1>
        <p class="text-xl text-gray-600">Extract all images from your PDF documents</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to extract images from</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Extract Options -->
    <div id="extractOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Image Extraction Settings</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Output Format -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Output Format</label>
                <select id="outputFormat" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="PNG">PNG (Best Quality)</option>
                    <option value="JPEG" selected>JPEG (Smaller Size)</option>
                    <option value="WEBP">WebP (Modern Format)</option>
                </select>
            </div>
            
            <!-- Image Quality -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Image Quality</label>
                <select id="imageQuality" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="95">High (95%)</option>
                    <option value="85" selected>Good (85%)</option>
                    <option value="75">Standard (75%)</option>
                    <option value="60">Low (60%)</option>
                </select>
            </div>
            
            <!-- Minimum Size Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Image Size (px)</label>
                <input type="number" id="minSize" value="50" min="10" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <p class="text-sm text-gray-500 mt-1">Skip images smaller than this size</p>
            </div>
            
            <!-- Page Range -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Page Range (Optional)</label>
                <input type="text" id="pageRange" placeholder="e.g., 1-5 or leave empty for all" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
        </div>
        
        <!-- Advanced Options -->
        <div class="bg-gray-50 rounded-lg p-6 mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Advanced Options</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="preserveTransparency" checked class="mr-2">
                    Preserve transparency (PNG only)
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="removeBackground" class="mr-2">
                    Remove white backgrounds
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="enhanceQuality" class="mr-2">
                    Enhance image quality
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="addMetadata" checked class="mr-2">
                    Include image metadata
                </label>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Images
            </button>
            <button id="extractBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-images mr-2"></i>Extract Images
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Extracting images from your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Images have been successfully extracted!</p>
            
            <!-- Extraction Stats -->
            <div id="extractionStats" class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-lg font-bold text-indigo-600" id="imageCount">0</div>
                        <div class="text-sm text-gray-600">Images Found</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-green-600" id="extractedCount">0</div>
                        <div class="text-sm text-gray-600">Images Extracted</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-purple-600" id="totalSize">0 MB</div>
                        <div class="text-sm text-gray-600">Total Size</div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>Download ZIP
                </button>
                <button id="newExtractionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Extract From Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-indigo-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-indigo-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-indigo-700">
            <li>Upload your PDF file</li>
            <li>Choose output format and quality settings</li>
            <li>Set minimum image size to filter small images</li>
            <li>Optionally specify page range to extract from</li>
            <li>Configure advanced options if needed</li>
            <li>Extract images and download the ZIP file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> Use PNG format for images with transparency or text. 
                Use JPEG for photographs to save space. WebP offers the best compression.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewImages);
        document.getElementById('extractBtn').addEventListener('click', extractImages);
        document.getElementById('newExtractionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('extractOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function previewImages() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const format = document.getElementById('outputFormat').value;
        const quality = document.getElementById('imageQuality').value;
        const minSize = document.getElementById('minSize').value;
        const pageRange = document.getElementById('pageRange').value;
        
        let message = `Image Extraction Preview:
        
Output Format: ${format}
Quality: ${quality}%
Minimum Size: ${minSize}px
Page Range: ${pageRange || 'All pages'}

The system will scan your PDF for images and extract them according to these settings.`;
        
        alert(message);
    }
    
    function extractImages() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('output_format', document.getElementById('outputFormat').value);
        formData.append('quality', document.getElementById('imageQuality').value);
        formData.append('min_size', document.getElementById('minSize').value);
        formData.append('page_range', document.getElementById('pageRange').value);
        formData.append('preserve_transparency', document.getElementById('preserveTransparency').checked);
        formData.append('remove_background', document.getElementById('removeBackground').checked);
        formData.append('enhance_quality', document.getElementById('enhanceQuality').checked);
        formData.append('add_metadata', document.getElementById('addMetadata').checked);
        
        // Show progress
        document.getElementById('extractOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/extract_images', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Image extraction failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Simulate extraction stats (in real implementation, these would come from the server)
            document.getElementById('imageCount').textContent = '12';
            document.getElementById('extractedCount').textContent = '10';
            document.getElementById('totalSize').textContent = (blob.size / 1024 / 1024).toFixed(2) + ' MB';
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'extracted_images.zip';
                a.click();
            };
        })
        .catch(error => {
            alert('Error extracting images: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('extractOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('extractOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset form values
        document.getElementById('outputFormat').value = 'JPEG';
        document.getElementById('imageQuality').value = '85';
        document.getElementById('minSize').value = '50';
        document.getElementById('pageRange').value = '';
        document.getElementById('preserveTransparency').checked = true;
        document.getElementById('removeBackground').checked = false;
        document.getElementById('enhanceQuality').checked = false;
        document.getElementById('addMetadata').checked = true;
    }
</script>
{% endblock %}
