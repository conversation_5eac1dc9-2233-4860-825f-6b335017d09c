import fitz  # PyMuPDF
from PIL import Image, ImageDraw, ImageFont
import io
import os
import tempfile
import uuid
from reportlab.pdfgen import canvas
from reportlab.lib.colors import Color

class CropEditorProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def crop_pdf_pages(self, file, crop_box, pages='all'):
        """Crop PDF pages to specified dimensions"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Parse pages
        if pages == 'all':
            page_list = list(range(pdf_doc.page_count))
        else:
            page_list = [int(p) - 1 for p in pages.split(',') if p.strip().isdigit()]
        
        for page_num in page_list:
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                
                # Create crop rectangle
                # crop_box format: [left, top, right, bottom] as percentages or absolute values
                page_rect = page.rect
                
                if crop_box.get('unit') == 'percent':
                    left = page_rect.x0 + (page_rect.width * crop_box['left'] / 100)
                    top = page_rect.y0 + (page_rect.height * crop_box['top'] / 100)
                    right = page_rect.x0 + (page_rect.width * crop_box['right'] / 100)
                    bottom = page_rect.y0 + (page_rect.height * crop_box['bottom'] / 100)
                else:
                    left = crop_box['left']
                    top = crop_box['top']
                    right = crop_box['right']
                    bottom = crop_box['bottom']
                
                # Apply crop
                crop_rect = fitz.Rect(left, top, right, bottom)
                page.set_cropbox(crop_rect)
        
        output_path = os.path.join(self.temp_dir, f'cropped_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def auto_crop_margins(self, file, margin_threshold=0.95):
        """Automatically crop white margins from PDF pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            
            # Get page as image for analysis
            mat = fitz.Matrix(2, 2)  # 2x scale for better analysis
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data)).convert('L')  # Convert to grayscale
            
            # Find content boundaries
            width, height = img.size
            pixels = list(img.getdata())
            
            # Find top boundary
            top_boundary = 0
            for y in range(height):
                row_start = y * width
                row_pixels = pixels[row_start:row_start + width]
                if any(pixel < 255 * margin_threshold for pixel in row_pixels):
                    top_boundary = y
                    break
            
            # Find bottom boundary
            bottom_boundary = height
            for y in range(height - 1, -1, -1):
                row_start = y * width
                row_pixels = pixels[row_start:row_start + width]
                if any(pixel < 255 * margin_threshold for pixel in row_pixels):
                    bottom_boundary = y
                    break
            
            # Find left boundary
            left_boundary = 0
            for x in range(width):
                column_pixels = [pixels[y * width + x] for y in range(height)]
                if any(pixel < 255 * margin_threshold for pixel in column_pixels):
                    left_boundary = x
                    break
            
            # Find right boundary
            right_boundary = width
            for x in range(width - 1, -1, -1):
                column_pixels = [pixels[y * width + x] for y in range(height)]
                if any(pixel < 255 * margin_threshold for pixel in column_pixels):
                    right_boundary = x
                    break
            
            # Convert back to PDF coordinates (accounting for 2x scale)
            page_rect = page.rect
            scale_x = page_rect.width / width
            scale_y = page_rect.height / height
            
            crop_left = page_rect.x0 + (left_boundary * scale_x)
            crop_top = page_rect.y0 + (top_boundary * scale_y)
            crop_right = page_rect.x0 + (right_boundary * scale_x)
            crop_bottom = page_rect.y0 + (bottom_boundary * scale_y)
            
            # Add small margin
            margin = 10
            crop_left = max(page_rect.x0, crop_left - margin)
            crop_top = max(page_rect.y0, crop_top - margin)
            crop_right = min(page_rect.x1, crop_right + margin)
            crop_bottom = min(page_rect.y1, crop_bottom + margin)
            
            # Apply crop if significant
            if (crop_right - crop_left) > page_rect.width * 0.1 and (crop_bottom - crop_top) > page_rect.height * 0.1:
                crop_rect = fitz.Rect(crop_left, crop_top, crop_right, crop_bottom)
                page.set_cropbox(crop_rect)
        
        output_path = os.path.join(self.temp_dir, f'auto_cropped_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_text_to_pdf(self, file, text_elements):
        """Add text elements to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for element in text_elements:
            page_num = element.get('page', 1) - 1
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                
                # Insert text
                page.insert_text(
                    (element['x'], element['y']),
                    element['text'],
                    fontsize=element.get('font_size', 12),
                    color=element.get('color', (0, 0, 0)),
                    fontname=element.get('font', 'helv'),
                    overlay=True
                )
        
        output_path = os.path.join(self.temp_dir, f'text_added_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_images_to_pdf(self, file, image_elements):
        """Add image elements to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for element in image_elements:
            page_num = element.get('page', 1) - 1
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                
                # Process image
                img_data = element['image'].read()
                img = Image.open(io.BytesIO(img_data))
                
                # Scale image if needed
                if 'width' in element or 'height' in element:
                    new_width = element.get('width', img.width)
                    new_height = element.get('height', img.height)
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Save temporary image
                img_path = os.path.join(self.temp_dir, f'temp_img_{uuid.uuid4().hex}.png')
                img.save(img_path)
                
                # Insert image
                img_rect = fitz.Rect(
                    element['x'], 
                    element['y'], 
                    element['x'] + img.width, 
                    element['y'] + img.height
                )
                page.insert_image(img_rect, filename=img_path)
        
        output_path = os.path.join(self.temp_dir, f'images_added_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_shapes_to_pdf(self, file, shape_elements):
        """Add shape elements to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for element in shape_elements:
            page_num = element.get('page', 1) - 1
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                
                color = element.get('color', (0, 0, 0))
                width = element.get('width', 1)
                fill_color = element.get('fill_color', None)
                
                if element['type'] == 'rectangle':
                    rect = fitz.Rect(
                        element['x'], element['y'],
                        element['x'] + element['width'], 
                        element['y'] + element['height']
                    )
                    page.draw_rect(rect, color=color, width=width, fill=fill_color)
                
                elif element['type'] == 'circle':
                    center = fitz.Point(element['x'], element['y'])
                    page.draw_circle(center, element['radius'], color=color, width=width, fill=fill_color)
                
                elif element['type'] == 'line':
                    start = fitz.Point(element['x1'], element['y1'])
                    end = fitz.Point(element['x2'], element['y2'])
                    page.draw_line(start, end, color=color, width=width)
        
        output_path = os.path.join(self.temp_dir, f'shapes_added_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_free_drawing(self, file, drawing_data):
        """Add free-hand drawing to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for drawing in drawing_data:
            page_num = drawing.get('page', 1) - 1
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                
                # Drawing consists of connected line segments
                points = drawing['points']
                color = drawing.get('color', (0, 0, 0))
                width = drawing.get('width', 2)
                
                # Draw connected lines
                for i in range(len(points) - 1):
                    start = fitz.Point(points[i]['x'], points[i]['y'])
                    end = fitz.Point(points[i + 1]['x'], points[i + 1]['y'])
                    page.draw_line(start, end, color=color, width=width)
        
        output_path = os.path.join(self.temp_dir, f'drawing_added_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def highlight_text(self, file, highlight_data):
        """Add text highlights to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for highlight in highlight_data:
            page_num = highlight.get('page', 1) - 1
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                
                # Create highlight annotation
                rect = fitz.Rect(
                    highlight['x1'], highlight['y1'],
                    highlight['x2'], highlight['y2']
                )
                
                color = highlight.get('color', (1, 1, 0))  # Yellow default
                highlight_annot = page.add_highlight_annot(rect)
                highlight_annot.set_colors(stroke=color)
                highlight_annot.update()
        
        output_path = os.path.join(self.temp_dir, f'highlighted_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def get_page_dimensions(self, file):
        """Get dimensions of all pages for crop planning"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        dimensions = []
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            rect = page.rect
            
            dimensions.append({
                'page': page_num + 1,
                'width': rect.width,
                'height': rect.height,
                'x0': rect.x0,
                'y0': rect.y0,
                'x1': rect.x1,
                'y1': rect.y1,
                'rotation': page.rotation
            })
        
        pdf_doc.close()
        return dimensions
