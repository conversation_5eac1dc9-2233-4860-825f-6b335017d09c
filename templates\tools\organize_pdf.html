{% extends "base.html" %}

{% block title %}{{ t.tools_list.organize_pdf.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-sort text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.organize_pdf.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.organize_pdf.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to organize its pages</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Organization Tools -->
    <div id="organizationTools" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <div class="flex flex-wrap justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">Page Organization</h2>
            <div class="flex space-x-2 mt-4 md:mt-0">
                <button id="selectAllBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                    <i class="fas fa-check-square mr-1"></i>Select All
                </button>
                <button id="deselectAllBtn" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm">
                    <i class="fas fa-square mr-1"></i>Deselect All
                </button>
                <button id="reverseOrderBtn" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm">
                    <i class="fas fa-exchange-alt mr-1"></i>Reverse Order
                </button>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg">
            <button id="rotateLeftBtn" class="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm">
                <i class="fas fa-undo mr-1"></i>Rotate Left
            </button>
            <button id="rotateRightBtn" class="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm">
                <i class="fas fa-redo mr-1"></i>Rotate Right
            </button>
            <button id="duplicateBtn" class="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                <i class="fas fa-copy mr-1"></i>Duplicate
            </button>
            <button id="deleteBtn" class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm">
                <i class="fas fa-trash mr-1"></i>Delete
            </button>
            <button id="extractBtn" class="px-3 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors text-sm">
                <i class="fas fa-file-export mr-1"></i>Extract
            </button>
        </div>
        
        <!-- Page Thumbnails -->
        <div id="pageThumbnails" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
            <!-- Thumbnails will be populated here -->
        </div>
        
        <!-- Organization Options -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Organization Options:</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="sortBySize" class="p-4 border border-gray-300 rounded-lg hover:bg-white transition-colors text-left">
                    <i class="fas fa-expand-arrows-alt text-blue-600 mb-2"></i>
                    <h4 class="font-medium">Sort by Size</h4>
                    <p class="text-sm text-gray-600">Arrange pages by dimensions</p>
                </button>
                <button id="sortByOrientation" class="p-4 border border-gray-300 rounded-lg hover:bg-white transition-colors text-left">
                    <i class="fas fa-mobile-alt text-green-600 mb-2"></i>
                    <h4 class="font-medium">Group by Orientation</h4>
                    <p class="text-sm text-gray-600">Portrait pages first, then landscape</p>
                </button>
                <button id="sortByContent" class="p-4 border border-gray-300 rounded-lg hover:bg-white transition-colors text-left">
                    <i class="fas fa-file-alt text-purple-600 mb-2"></i>
                    <h4 class="font-medium">Sort by Content</h4>
                    <p class="text-sm text-gray-600">Pages with more content first</p>
                </button>
            </div>
        </div>
        
        <!-- Apply Changes -->
        <div class="flex justify-center space-x-4">
            <button id="previewChangesBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Changes
            </button>
            <button id="applyChangesBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-save mr-2"></i>Apply Changes
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Organizing your PDF pages...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully organized!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newOrganizeBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Organize Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-purple-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-purple-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-purple-700">
            <li>Upload your PDF file to see page thumbnails</li>
            <li>Select pages by clicking on them (blue border indicates selection)</li>
            <li>Use action buttons to rotate, duplicate, or delete selected pages</li>
            <li>Drag and drop pages to reorder them manually</li>
            <li>Use quick organization options for automatic sorting</li>
            <li>Preview your changes before applying them</li>
            <li>Download your organized PDF</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> You can select multiple pages by holding Ctrl (Cmd on Mac) while clicking. 
                Drag pages to reorder them, and use the action buttons to modify selected pages.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    let selectedFile = null;
    let thumbnails = [];
    let selectedPages = new Set();
    let sortable = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Action buttons
        document.getElementById('selectAllBtn').addEventListener('click', selectAllPages);
        document.getElementById('deselectAllBtn').addEventListener('click', deselectAllPages);
        document.getElementById('reverseOrderBtn').addEventListener('click', reversePageOrder);
        document.getElementById('rotateLeftBtn').addEventListener('click', () => rotateSelectedPages(-90));
        document.getElementById('rotateRightBtn').addEventListener('click', () => rotateSelectedPages(90));
        document.getElementById('duplicateBtn').addEventListener('click', duplicateSelectedPages);
        document.getElementById('deleteBtn').addEventListener('click', deleteSelectedPages);
        document.getElementById('extractBtn').addEventListener('click', extractSelectedPages);
        
        // Quick organization
        document.getElementById('sortBySize').addEventListener('click', () => quickSort('size'));
        document.getElementById('sortByOrientation').addEventListener('click', () => quickSort('orientation'));
        document.getElementById('sortByContent').addEventListener('click', () => quickSort('content'));
        
        // Apply changes
        document.getElementById('previewChangesBtn').addEventListener('click', previewChanges);
        document.getElementById('applyChangesBtn').addEventListener('click', applyChanges);
        document.getElementById('newOrganizeBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            loadThumbnails();
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function loadThumbnails() {
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        // Show loading
        const thumbnailsContainer = document.getElementById('pageThumbnails');
        thumbnailsContainer.innerHTML = '<div class="col-span-full text-center py-8"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div><p class="text-gray-600">Loading page thumbnails...</p></div>';
        
        fetch('/api/get_pdf_thumbnails', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            thumbnails = data.thumbnails;
            displayThumbnails();
            document.getElementById('organizationTools').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading thumbnails:', error);
            thumbnailsContainer.innerHTML = '<div class="col-span-full text-center py-8 text-red-600">Error loading thumbnails. Please try again.</div>';
        });
    }
    
    function displayThumbnails() {
        const container = document.getElementById('pageThumbnails');
        container.innerHTML = '';
        
        thumbnails.forEach((thumb, index) => {
            const thumbDiv = document.createElement('div');
            thumbDiv.className = 'thumbnail-item bg-white border-2 border-gray-200 rounded-lg p-3 cursor-pointer hover:shadow-lg transition-all duration-300';
            thumbDiv.dataset.pageIndex = thumb.original_index;
            thumbDiv.dataset.currentIndex = index;
            
            thumbDiv.innerHTML = `
                <div class="relative">
                    <img src="${thumb.thumbnail_base64}" alt="Page ${thumb.page_number}" 
                         class="w-full h-auto rounded border ${thumb.rotation !== 0 ? 'transform rotate-' + thumb.rotation : ''}">
                    <div class="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        ${thumb.page_number}
                    </div>
                    <div class="absolute top-2 right-2 page-controls opacity-0 transition-opacity duration-300">
                        <button class="rotate-btn bg-blue-600 text-white p-1 rounded text-xs mr-1" data-rotation="90">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="delete-btn bg-red-600 text-white p-1 rounded text-xs">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-2 text-center">
                    <p class="text-sm font-medium text-gray-800">Page ${thumb.page_number}</p>
                    <p class="text-xs text-gray-500">${thumb.width}×${thumb.height}</p>
                </div>
            `;
            
            // Add click handler for selection
            thumbDiv.addEventListener('click', (e) => {
                if (!e.target.closest('.page-controls')) {
                    togglePageSelection(thumb.original_index, thumbDiv);
                }
            });
            
            // Add hover effect for controls
            thumbDiv.addEventListener('mouseenter', () => {
                thumbDiv.querySelector('.page-controls').classList.remove('opacity-0');
            });
            
            thumbDiv.addEventListener('mouseleave', () => {
                thumbDiv.querySelector('.page-controls').classList.add('opacity-0');
            });
            
            // Add individual control handlers
            thumbDiv.querySelector('.rotate-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                rotatePage(thumb.original_index, 90);
            });
            
            thumbDiv.querySelector('.delete-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                deletePage(thumb.original_index);
            });
            
            container.appendChild(thumbDiv);
        });
        
        // Initialize sortable
        if (sortable) {
            sortable.destroy();
        }
        
        sortable = Sortable.create(container, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: function(evt) {
                // Update thumbnails array order
                const item = thumbnails.splice(evt.oldIndex, 1)[0];
                thumbnails.splice(evt.newIndex, 0, item);
                updateThumbnailIndices();
            }
        });
    }
    
    function togglePageSelection(pageIndex, element) {
        if (selectedPages.has(pageIndex)) {
            selectedPages.delete(pageIndex);
            element.classList.remove('border-blue-500', 'bg-blue-50');
            element.classList.add('border-gray-200');
        } else {
            selectedPages.add(pageIndex);
            element.classList.remove('border-gray-200');
            element.classList.add('border-blue-500', 'bg-blue-50');
        }
        
        updateSelectionUI();
    }
    
    function updateSelectionUI() {
        const selectedCount = selectedPages.size;
        const actionButtons = ['rotateLeftBtn', 'rotateRightBtn', 'duplicateBtn', 'deleteBtn', 'extractBtn'];
        
        actionButtons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (selectedCount > 0) {
                btn.disabled = false;
                btn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                btn.disabled = true;
                btn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        });
    }
    
    function selectAllPages() {
        selectedPages.clear();
        thumbnails.forEach(thumb => {
            selectedPages.add(thumb.original_index);
        });
        
        document.querySelectorAll('.thumbnail-item').forEach(item => {
            item.classList.remove('border-gray-200');
            item.classList.add('border-blue-500', 'bg-blue-50');
        });
        
        updateSelectionUI();
    }
    
    function deselectAllPages() {
        selectedPages.clear();
        
        document.querySelectorAll('.thumbnail-item').forEach(item => {
            item.classList.remove('border-blue-500', 'bg-blue-50');
            item.classList.add('border-gray-200');
        });
        
        updateSelectionUI();
    }
    
    function reversePageOrder() {
        thumbnails.reverse();
        displayThumbnails();
    }
    
    function rotateSelectedPages(degrees) {
        if (selectedPages.size === 0) {
            alert('Please select pages to rotate.');
            return;
        }
        
        thumbnails.forEach(thumb => {
            if (selectedPages.has(thumb.original_index)) {
                thumb.rotation = (thumb.rotation + degrees) % 360;
            }
        });
        
        displayThumbnails();
    }
    
    function rotatePage(pageIndex, degrees) {
        const thumb = thumbnails.find(t => t.original_index === pageIndex);
        if (thumb) {
            thumb.rotation = (thumb.rotation + degrees) % 360;
            displayThumbnails();
        }
    }
    
    function duplicateSelectedPages() {
        if (selectedPages.size === 0) {
            alert('Please select pages to duplicate.');
            return;
        }
        
        const newThumbnails = [];
        thumbnails.forEach(thumb => {
            newThumbnails.push(thumb);
            if (selectedPages.has(thumb.original_index)) {
                // Create duplicate
                const duplicate = { ...thumb };
                duplicate.page_number = `${thumb.page_number} (Copy)`;
                newThumbnails.push(duplicate);
            }
        });
        
        thumbnails = newThumbnails;
        displayThumbnails();
    }
    
    function deleteSelectedPages() {
        if (selectedPages.size === 0) {
            alert('Please select pages to delete.');
            return;
        }
        
        if (confirm(`Are you sure you want to delete ${selectedPages.size} page(s)?`)) {
            thumbnails = thumbnails.filter(thumb => !selectedPages.has(thumb.original_index));
            selectedPages.clear();
            displayThumbnails();
        }
    }
    
    function deletePage(pageIndex) {
        if (confirm('Are you sure you want to delete this page?')) {
            thumbnails = thumbnails.filter(thumb => thumb.original_index !== pageIndex);
            selectedPages.delete(pageIndex);
            displayThumbnails();
        }
    }
    
    function extractSelectedPages() {
        if (selectedPages.size === 0) {
            alert('Please select pages to extract.');
            return;
        }
        
        alert(`Extract functionality would create a new PDF with ${selectedPages.size} selected page(s).`);
    }
    
    function quickSort(criteria) {
        // This would call the backend to sort pages
        alert(`Quick sort by ${criteria} would be implemented here.`);
    }
    
    function updateThumbnailIndices() {
        thumbnails.forEach((thumb, index) => {
            thumb.current_index = index;
        });
    }
    
    function previewChanges() {
        alert('Preview would show a summary of changes to be applied.');
    }
    
    function applyChanges() {
        if (!selectedFile) {
            alert('No file selected.');
            return;
        }
        
        // Prepare data for reorganization
        const pageOrder = thumbnails.map(thumb => thumb.original_index);
        const rotations = {};
        const deletions = [];
        
        thumbnails.forEach(thumb => {
            if (thumb.rotation !== 0) {
                rotations[thumb.original_index] = thumb.rotation;
            }
        });
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('page_order', pageOrder.join(','));
        formData.append('rotations', JSON.stringify(rotations));
        formData.append('deletions', JSON.stringify(deletions));
        
        // Show progress
        document.getElementById('organizationTools').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/reorganize_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Organization failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'organized.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error organizing PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('organizationTools').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        thumbnails = [];
        selectedPages.clear();
        
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('organizationTools').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        if (sortable) {
            sortable.destroy();
            sortable = null;
        }
    }
</script>

<style>
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    transform: scale(1.05);
}

.sortable-drag {
    transform: rotate(5deg);
}

.thumbnail-item {
    transition: all 0.3s ease;
}

.thumbnail-item:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
