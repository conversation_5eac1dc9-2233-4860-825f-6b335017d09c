from flask import Flask, render_template, request, jsonify, send_file, session
from flask_cors import CORS
import os
import json
from datetime import datetime
import uuid
from werkzeug.utils import secure_filename
import tempfile
import shutil

# Import PDF processing modules
from utils.pdf_processor import PDFProcessor
from utils.image_processor import ImageProcessor
from utils.watermark_processor import WatermarkProcessor
from utils.translator import PDFTranslator
from utils.text_processor import TextProcessor

app = Flask(__name__)
app.secret_key = 'pdf2any_secret_key_2025'
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
PROCESSED_FOLDER = 'processed'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)
os.makedirs('static/temp', exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_language():
    return session.get('language', 'en')

def load_translations():
    lang = get_language()
    try:
        with open(f'translations/{lang}.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        with open('translations/en.json', 'r', encoding='utf-8') as f:
            return json.load(f)

@app.route('/')
def index():
    translations = load_translations()
    return render_template('index.html', t=translations, lang=get_language())

@app.route('/set_language/<lang>')
def set_language(lang):
    session['language'] = lang
    return jsonify({'status': 'success'})

@app.route('/tool/<tool_name>')
def tool_page(tool_name):
    translations = load_translations()
    return render_template(f'tools/{tool_name}.html', t=translations, lang=get_language(), tool=tool_name)

# PDF Processing Routes
@app.route('/api/merge_pdf', methods=['POST'])
def merge_pdf():
    try:
        files = request.files.getlist('files')
        if len(files) < 2:
            return jsonify({'error': 'At least 2 files required'}), 400
        
        processor = PDFProcessor()
        result_path = processor.merge_pdfs([f for f in files if allowed_file(f.filename)])
        
        return send_file(result_path, as_attachment=True, download_name='merged.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/split_pdf', methods=['POST'])
def split_pdf():
    try:
        file = request.files['file']
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type'}), 400
        
        processor = PDFProcessor()
        result_paths = processor.split_pdf(file)
        
        # Create zip file with all split PDFs
        zip_path = processor.create_zip(result_paths, 'split_pages.zip')
        return send_file(zip_path, as_attachment=True, download_name='split_pages.zip')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/compress_pdf', methods=['POST'])
def compress_pdf():
    try:
        file = request.files['file']
        quality = request.form.get('quality', 'medium')
        
        processor = PDFProcessor()
        result_path = processor.compress_pdf(file, quality)
        
        return send_file(result_path, as_attachment=True, download_name='compressed.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/protect_pdf', methods=['POST'])
def protect_pdf():
    try:
        file = request.files['file']
        password = request.form.get('password')
        
        processor = PDFProcessor()
        result_path = processor.protect_pdf(file, password)
        
        return send_file(result_path, as_attachment=True, download_name='protected.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/add_page_numbers', methods=['POST'])
def add_page_numbers():
    try:
        file = request.files['file']
        position = request.form.get('position', 'bottom-right')
        start_page = int(request.form.get('start_page', 1))

        processor = PDFProcessor()
        result_path = processor.add_page_numbers(file, position, start_page)

        return send_file(result_path, as_attachment=True, download_name='numbered.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/remove_pages', methods=['POST'])
def remove_pages():
    try:
        file = request.files['file']
        pages = request.form.get('pages', '').split(',')
        pages = [p.strip() for p in pages if p.strip().isdigit()]

        if not pages:
            return jsonify({'error': 'No valid pages specified'}), 400

        processor = PDFProcessor()
        result_path = processor.remove_pages(file, pages)

        return send_file(result_path, as_attachment=True, download_name='pages_removed.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/extract_pages', methods=['POST'])
def extract_pages():
    try:
        file = request.files['file']
        pages = request.form.get('pages', '').split(',')
        pages = [p.strip() for p in pages if p.strip().isdigit()]

        if not pages:
            return jsonify({'error': 'No valid pages specified'}), 400

        processor = PDFProcessor()
        result_path = processor.extract_pages(file, pages)

        return send_file(result_path, as_attachment=True, download_name='extracted_pages.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rotate_pdf', methods=['POST'])
def rotate_pdf():
    try:
        file = request.files['file']
        rotation = int(request.form.get('rotation', 90))

        processor = PDFProcessor()
        result_path = processor.rotate_pdf(file, rotation)

        return send_file(result_path, as_attachment=True, download_name='rotated.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/unlock_pdf', methods=['POST'])
def unlock_pdf():
    try:
        file = request.files['file']
        password = request.form.get('password')

        if not password:
            return jsonify({'error': 'Password required'}), 400

        processor = PDFProcessor()
        result_path = processor.unlock_pdf(file, password)

        return send_file(result_path, as_attachment=True, download_name='unlocked.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Image Processing Routes
@app.route('/api/pdf_to_images', methods=['POST'])
def pdf_to_images():
    try:
        file = request.files['file']
        format = request.form.get('format', 'PNG')
        quality = int(request.form.get('quality', 95))
        dpi = int(request.form.get('dpi', 150))

        processor = ImageProcessor()
        image_paths = processor.pdf_to_images(file, format, quality, dpi)

        if len(image_paths) == 1:
            return send_file(image_paths[0], as_attachment=True)
        else:
            zip_path = processor.create_zip(image_paths, 'pdf_images.zip')
            return send_file(zip_path, as_attachment=True, download_name='pdf_images.zip')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/images_to_pdf', methods=['POST'])
def images_to_pdf():
    try:
        files = request.files.getlist('files')
        page_size = request.form.get('page_size', 'A4')
        orientation = request.form.get('orientation', 'portrait')

        processor = ImageProcessor()
        result_path = processor.images_to_pdf(files, page_size, orientation)

        return send_file(result_path, as_attachment=True, download_name='images_to_pdf.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/extract_images', methods=['POST'])
def extract_images():
    try:
        file = request.files['file']

        processor = ImageProcessor()
        image_paths = processor.extract_images_from_pdf(file)

        if not image_paths:
            return jsonify({'error': 'No images found in PDF'}), 400

        if len(image_paths) == 1:
            return send_file(image_paths[0], as_attachment=True)
        else:
            zip_path = processor.create_zip(image_paths, 'extracted_images.zip')
            return send_file(zip_path, as_attachment=True, download_name='extracted_images.zip')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Watermark Routes
@app.route('/api/add_text_watermark', methods=['POST'])
def add_text_watermark():
    try:
        file = request.files['file']
        text = request.form.get('text', 'WATERMARK')
        font_size = int(request.form.get('font_size', 50))
        opacity = float(request.form.get('opacity', 0.5))
        color = request.form.get('color', '0,0,0').split(',')
        color = tuple(float(c) for c in color)
        rotation = int(request.form.get('rotation', 45))
        position = request.form.get('position', 'center')
        pages = request.form.get('pages', 'all')

        processor = WatermarkProcessor()
        result_path = processor.add_text_watermark(file, text, font_size, opacity, color, rotation, position, pages)

        return send_file(result_path, as_attachment=True, download_name='watermarked.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/add_image_watermark', methods=['POST'])
def add_image_watermark():
    try:
        file = request.files['file']
        image_file = request.files['image']
        scale = float(request.form.get('scale', 0.5))
        opacity = float(request.form.get('opacity', 0.5))
        position = request.form.get('position', 'center')
        rotation = int(request.form.get('rotation', 0))
        pages = request.form.get('pages', 'all')

        processor = WatermarkProcessor()
        result_path = processor.add_image_watermark(file, image_file, scale, opacity, position, rotation, pages)

        return send_file(result_path, as_attachment=True, download_name='watermarked.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/add_stamp', methods=['POST'])
def add_stamp():
    try:
        file = request.files['file']
        stamp_text = request.form.get('stamp_text', 'APPROVED')
        opacity = float(request.form.get('opacity', 0.7))
        scale = float(request.form.get('scale', 1.0))
        position = request.form.get('position', 'center')
        rotation = int(request.form.get('rotation', 0))
        pages = request.form.get('pages', 'all')

        processor = WatermarkProcessor()
        result_path = processor.add_stamp(file, stamp_text, opacity, scale, position, rotation, pages)

        return send_file(result_path, as_attachment=True, download_name='stamped.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/get_stamps')
def get_stamps():
    processor = WatermarkProcessor()
    stamps = processor.get_available_stamps()
    return jsonify({'stamps': stamps})

# Translation Routes
@app.route('/api/translate_pdf', methods=['POST'])
def translate_pdf():
    try:
        file = request.files['file']
        target_lang = request.form.get('target_lang', 'en')

        translator = PDFTranslator()
        translation_data = translator.prepare_translation_data(file, target_lang)

        return jsonify(translation_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/create_translation_html', methods=['POST'])
def create_translation_html():
    try:
        file = request.files['file']
        target_lang = request.form.get('target_lang', 'en')

        translator = PDFTranslator()
        translation_data = translator.prepare_translation_data(file, target_lang)
        html_path = translator.create_translation_html(translation_data)

        return send_file(html_path, as_attachment=True, download_name='translation.html')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# QR Code Routes
@app.route('/api/create_qr_pdf', methods=['POST'])
def create_qr_pdf():
    try:
        text = request.form.get('text', '')
        title = request.form.get('title', 'QR Code')

        if not text:
            return jsonify({'error': 'Text is required'}), 400

        processor = ImageProcessor()
        result_path = processor.image_to_qr_pdf(text, title)

        return send_file(result_path, as_attachment=True, download_name='qr_code.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Text Processing Routes
@app.route('/api/text_to_pdf', methods=['POST'])
def text_to_pdf():
    try:
        text = request.form.get('text', '')
        font_family = request.form.get('font_family', 'Helvetica')
        font_size = int(request.form.get('font_size', 12))
        page_size = request.form.get('page_size', 'A4')

        if not text.strip():
            return jsonify({'error': 'Text content is required'}), 400

        processor = TextProcessor()
        result_path = processor.text_to_pdf(text, font_family, font_size, page_size)

        return send_file(result_path, as_attachment=True, download_name='text_document.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/extract_text', methods=['POST'])
def extract_text():
    try:
        file = request.files['file']

        processor = TextProcessor()
        extracted_text = processor.extract_text_from_pdf(file)

        return jsonify({'text': extracted_text})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/create_formatted_pdf', methods=['POST'])
def create_formatted_pdf():
    try:
        title = request.form.get('title', '')
        content = request.form.get('content', '')
        author = request.form.get('author', '')
        subject = request.form.get('subject', '')

        if not content.strip():
            return jsonify({'error': 'Content is required'}), 400

        processor = TextProcessor()
        result_path = processor.create_formatted_pdf(title, content, author, subject)

        return send_file(result_path, as_attachment=True, download_name='formatted_document.pdf')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# OCR Route (Simple text extraction)
@app.route('/api/pdf_to_ocr', methods=['POST'])
def pdf_to_ocr():
    try:
        file = request.files['file']
        language = request.form.get('language', 'en')

        # For now, we'll use the same text extraction as we don't have Tesseract
        processor = TextProcessor()
        extracted_text = processor.extract_text_from_pdf(file)

        # Detect language
        translator = PDFTranslator()
        detected_lang = translator.detect_language(extracted_text)

        return jsonify({
            'text': extracted_text,
            'detected_language': detected_lang,
            'word_count': len(extracted_text.split()),
            'character_count': len(extracted_text)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
