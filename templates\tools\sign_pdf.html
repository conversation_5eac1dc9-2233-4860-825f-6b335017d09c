{% extends "base.html" %}

{% block title %}{{ t.tools_list.sign_pdf.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-signature text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.sign_pdf.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.sign_pdf.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to add digital signature</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Signature Options -->
    <div id="signatureOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Signature Options</h2>
        
        <!-- Signature Type Tabs -->
        <div class="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-8">
            <button id="textSignTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors bg-white text-green-600 shadow-sm">
                <i class="fas fa-font mr-2"></i>Text Signature
            </button>
            <button id="imageSignTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors text-gray-600 hover:text-gray-800">
                <i class="fas fa-image mr-2"></i>Image Signature
            </button>
            <button id="completeSignTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors text-gray-600 hover:text-gray-800">
                <i class="fas fa-pen-fancy mr-2"></i>Complete Signature
            </button>
        </div>
        
        <!-- Text Signature Panel -->
        <div id="textSignPanel" class="signature-panel">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Signature Text</label>
                    <input type="text" id="signatureText" placeholder="Enter your signature text" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                           style="font-family: 'Brush Script MT', cursive; font-size: 18px;">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                    <input type="range" id="textSignSize" min="12" max="48" value="24" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="textSignSizeValue">24</span>px</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Text Color</label>
                    <input type="color" id="textSignColor" value="#0000ff" class="w-full h-10 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Page Number</label>
                    <select id="textSignPage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="">Last Page (Default)</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Position X</label>
                    <input type="number" id="textSignX" value="100" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Position Y</label>
                    <input type="number" id="textSignY" value="100" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
            </div>
        </div>
        
        <!-- Image Signature Panel -->
        <div id="imageSignPanel" class="signature-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Signature Image</label>
                    <input type="file" id="signatureImage" accept="image/*" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                    <p class="text-sm text-gray-500 mt-1">Upload PNG image with transparent background for best results</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Scale</label>
                    <input type="range" id="imageSignScale" min="0.1" max="2" step="0.1" value="1" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="imageSignScaleValue">100</span>%</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Page Number</label>
                    <select id="imageSignPage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="">Last Page (Default)</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Position X</label>
                    <input type="number" id="imageSignX" value="100" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Position Y</label>
                    <input type="number" id="imageSignY" value="100" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
            </div>
        </div>
        
        <!-- Complete Signature Panel -->
        <div id="completeSignPanel" class="signature-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Signature Text</label>
                    <input type="text" id="completeSignText" placeholder="Your name" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                           style="font-family: 'Brush Script MT', cursive;">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Signature Image (Optional)</label>
                    <input type="file" id="completeSignImage" accept="image/*" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Signature Label</label>
                    <input type="text" id="signatureLabel" placeholder="e.g., Signature, Authorized by" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                    <select id="dateFormat" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="%Y-%m-%d">2025-01-21</option>
                        <option value="%d/%m/%Y">21/01/2025</option>
                        <option value="%B %d, %Y">January 21, 2025</option>
                        <option value="%d %B %Y">21 January 2025</option>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Page Number</label>
                    <select id="completeSignPage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="1">Last Page (Default)</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Position X</label>
                    <input type="number" id="completeSignX" value="100" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Position Y</label>
                    <input type="number" id="completeSignY" value="100" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Text Size</label>
                    <input type="range" id="completeTextSize" min="10" max="24" value="16" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="completeTextSizeValue">16</span>px</div>
                </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Signature Options:</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="addDate" checked class="mr-2">
                        Add Date
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="addLine" checked class="mr-2">
                        Add Signature Line
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="addLabel" class="mr-2">
                        Add Label
                    </label>
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">Image Scale</label>
                        <input type="range" id="completeImageScale" min="0.1" max="1" step="0.1" value="0.5" class="w-full">
                        <div class="text-center text-xs text-gray-600"><span id="completeImageScaleValue">50</span>%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Suggested Positions -->
        <div id="suggestedPositions" class="mt-8 p-6 bg-blue-50 rounded-lg hidden">
            <h4 class="text-md font-semibold text-blue-800 mb-4">
                <i class="fas fa-lightbulb mr-2"></i>Suggested Signature Positions:
            </h4>
            <div id="positionsList" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Positions will be populated here -->
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="suggestPositionsBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-crosshairs mr-2"></i>Suggest Positions
            </button>
            <button id="previewSignBtn" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview
            </button>
            <button id="applySignatureBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-signature mr-2"></i>Apply Signature
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Adding signature to your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully signed!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newSignatureBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Sign Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-green-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-green-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-green-700">
            <li>Upload your PDF document that needs to be signed</li>
            <li>Choose signature type: Text, Image, or Complete signature</li>
            <li>Configure signature settings (position, size, colors)</li>
            <li>Use "Suggest Positions" to find optimal signature placement</li>
            <li>Preview your signature before applying</li>
            <li>Apply signature and download the signed PDF</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> For image signatures, use PNG files with transparent backgrounds for the best results. 
                Complete signatures include text, optional image, date, and signature line.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let currentTab = 'text';
    let pageCount = 0;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        setupRangeInputs();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Tab switching
        document.getElementById('textSignTab').addEventListener('click', () => switchTab('text'));
        document.getElementById('imageSignTab').addEventListener('click', () => switchTab('image'));
        document.getElementById('completeSignTab').addEventListener('click', () => switchTab('complete'));
        
        // Buttons
        document.getElementById('suggestPositionsBtn').addEventListener('click', suggestPositions);
        document.getElementById('previewSignBtn').addEventListener('click', previewSignature);
        document.getElementById('applySignatureBtn').addEventListener('click', applySignature);
        document.getElementById('newSignatureBtn').addEventListener('click', resetForm);
    }
    
    function setupRangeInputs() {
        const ranges = [
            { id: 'textSignSize', valueId: 'textSignSizeValue', suffix: 'px' },
            { id: 'imageSignScale', valueId: 'imageSignScaleValue', suffix: '%', multiplier: 100 },
            { id: 'completeTextSize', valueId: 'completeTextSizeValue', suffix: 'px' },
            { id: 'completeImageScale', valueId: 'completeImageScaleValue', suffix: '%', multiplier: 100 }
        ];
        
        ranges.forEach(range => {
            const input = document.getElementById(range.id);
            const valueDisplay = document.getElementById(range.valueId);
            
            if (input && valueDisplay) {
                input.addEventListener('input', function() {
                    let value = this.value;
                    if (range.multiplier) {
                        value = Math.round(value * range.multiplier);
                    }
                    valueDisplay.textContent = value;
                });
            }
        });
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            loadPageInfo();
            document.getElementById('signatureOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function loadPageInfo() {
        // This would typically load page count and populate page selectors
        // For now, we'll simulate it
        pageCount = 5; // This would come from the PDF
        
        const pageSelectors = ['textSignPage', 'imageSignPage', 'completeSignPage'];
        pageSelectors.forEach(selectorId => {
            const selector = document.getElementById(selectorId);
            selector.innerHTML = '<option value="">Last Page (Default)</option>';
            
            for (let i = 1; i <= pageCount; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Page ${i}`;
                selector.appendChild(option);
            }
        });
    }
    
    function switchTab(tab) {
        // Update tab buttons
        document.querySelectorAll('[id$="SignTab"]').forEach(btn => {
            btn.classList.remove('bg-white', 'text-green-600', 'shadow-sm');
            btn.classList.add('text-gray-600');
        });
        
        document.getElementById(tab + 'SignTab').classList.add('bg-white', 'text-green-600', 'shadow-sm');
        document.getElementById(tab + 'SignTab').classList.remove('text-gray-600');
        
        // Update panels
        document.querySelectorAll('.signature-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        document.getElementById(tab + 'SignPanel').classList.remove('hidden');
        currentTab = tab;
    }
    
    function suggestPositions() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        fetch('/api/get_signature_positions', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            displaySuggestedPositions(data.positions);
        })
        .catch(error => {
            console.error('Error getting positions:', error);
            // Show some default positions
            const defaultPositions = [
                { page: 1, name: 'Bottom Right', x: 400, y: 700, description: 'Traditional signature position' },
                { page: 1, name: 'Bottom Left', x: 50, y: 700, description: 'Left-aligned signature' },
                { page: 1, name: 'Bottom Center', x: 250, y: 700, description: 'Centered signature' }
            ];
            displaySuggestedPositions(defaultPositions);
        });
    }
    
    function displaySuggestedPositions(positions) {
        const positionsList = document.getElementById('positionsList');
        const suggestedDiv = document.getElementById('suggestedPositions');
        
        positionsList.innerHTML = '';
        
        positions.slice(0, 6).forEach(pos => {
            const posDiv = document.createElement('div');
            posDiv.className = 'p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors';
            posDiv.innerHTML = `
                <h5 class="font-medium text-blue-800">${pos.name}</h5>
                <p class="text-sm text-blue-600">Page ${pos.page}</p>
                <p class="text-xs text-gray-600">${pos.description}</p>
                <p class="text-xs text-gray-500 mt-1">X: ${pos.x}, Y: ${pos.y}</p>
            `;
            
            posDiv.onclick = () => applyPosition(pos);
            positionsList.appendChild(posDiv);
        });
        
        suggestedDiv.classList.remove('hidden');
    }
    
    function applyPosition(position) {
        const xInputs = ['textSignX', 'imageSignX', 'completeSignX'];
        const yInputs = ['textSignY', 'imageSignY', 'completeSignY'];
        const pageInputs = ['textSignPage', 'imageSignPage', 'completeSignPage'];
        
        xInputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) input.value = position.x;
        });
        
        yInputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) input.value = position.y;
        });
        
        pageInputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) input.value = position.page;
        });
        
        // Hide suggestions after selection
        document.getElementById('suggestedPositions').classList.add('hidden');
    }
    
    function previewSignature() {
        alert('Preview functionality would show a visual representation of the signature placement on the PDF.');
    }
    
    function applySignature() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('signature_type', currentTab);
        
        if (currentTab === 'text') {
            const text = document.getElementById('signatureText').value;
            if (!text.trim()) {
                alert('Please enter signature text.');
                return;
            }
            
            formData.append('signature_text', text);
            formData.append('x', document.getElementById('textSignX').value);
            formData.append('y', document.getElementById('textSignY').value);
            formData.append('font_size', document.getElementById('textSignSize').value);
            
            const pageNum = document.getElementById('textSignPage').value;
            if (pageNum) formData.append('page_num', pageNum);
            
        } else if (currentTab === 'image') {
            const imageFile = document.getElementById('signatureImage').files[0];
            if (!imageFile) {
                alert('Please select a signature image.');
                return;
            }
            
            formData.append('signature_image', imageFile);
            formData.append('x', document.getElementById('imageSignX').value);
            formData.append('y', document.getElementById('imageSignY').value);
            formData.append('scale', document.getElementById('imageSignScale').value);
            
            const pageNum = document.getElementById('imageSignPage').value;
            if (pageNum) formData.append('page_num', pageNum);
            
        } else if (currentTab === 'complete') {
            const text = document.getElementById('completeSignText').value;
            if (!text.trim()) {
                alert('Please enter signature text.');
                return;
            }
            
            formData.append('signature_text', text);
            formData.append('x', document.getElementById('completeSignX').value);
            formData.append('y', document.getElementById('completeSignY').value);
            formData.append('page_num', document.getElementById('completeSignPage').value || '1');
            formData.append('text_size', document.getElementById('completeTextSize').value);
            formData.append('date_format', document.getElementById('dateFormat').value);
            formData.append('signature_label', document.getElementById('signatureLabel').value);
            formData.append('add_date', document.getElementById('addDate').checked);
            formData.append('add_line', document.getElementById('addLine').checked);
            
            const imageFile = document.getElementById('completeSignImage').files[0];
            if (imageFile) {
                formData.append('signature_image', imageFile);
                formData.append('image_scale', document.getElementById('completeImageScale').value);
            }
        }
        
        // Show progress
        document.getElementById('signatureOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/sign_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Signature application failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'signed.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error applying signature: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('signatureOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('signatureOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        document.getElementById('suggestedPositions').classList.add('hidden');
        
        // Reset form fields
        document.getElementById('signatureText').value = '';
        document.getElementById('signatureImage').value = '';
        document.getElementById('completeSignText').value = '';
        document.getElementById('completeSignImage').value = '';
    }
</script>
{% endblock %}
