from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import tempfile
import uuid
import fitz  # PyMuPDF
import re

class TextProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        
        # Try to register fonts
        try:
            # Register Arial font for better text support
            pdfmetrics.registerFont(TTFont('Arial', 'arial.ttf'))
            pdfmetrics.registerFont(TTFont('Arial-Bold', 'arialbd.ttf'))
        except:
            pass  # Use default fonts if custom fonts not available
    
    def text_to_pdf(self, text, font_family='Helvetica', font_size=12, 
                   page_size='A4', margins=72, line_spacing=1.2):
        """Convert text to PDF"""
        
        # Set page size
        if page_size == 'A4':
            page_size = A4
        else:
            page_size = letter
        
        output_path = os.path.join(self.temp_dir, f'text_to_pdf_{uuid.uuid4().hex}.pdf')
        
        # Create document
        doc = SimpleDocTemplate(
            output_path,
            pagesize=page_size,
            rightMargin=margins,
            leftMargin=margins,
            topMargin=margins,
            bottomMargin=margins
        )
        
        # Create styles
        styles = getSampleStyleSheet()
        
        # Custom style for body text
        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontName=font_family,
            fontSize=font_size,
            leading=font_size * line_spacing,
            spaceAfter=12,
            alignment=0  # Left alignment
        )
        
        # Process text into paragraphs
        story = []
        paragraphs = text.split('\n\n')
        
        for para_text in paragraphs:
            if para_text.strip():
                # Clean up the text
                para_text = para_text.strip().replace('\n', ' ')
                
                # Create paragraph
                para = Paragraph(para_text, body_style)
                story.append(para)
                story.append(Spacer(1, 6))
        
        # Build PDF
        doc.build(story)
        
        return output_path
    
    def extract_text_from_pdf(self, file):
        """Extract text from PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        extracted_text = ""
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            text = page.get_text()
            
            if text.strip():
                extracted_text += f"\n--- Page {page_num + 1} ---\n"
                extracted_text += text + "\n"
        
        pdf_doc.close()
        return extracted_text
    
    def create_formatted_pdf(self, title, content, author="", subject=""):
        """Create a formatted PDF with title and content"""
        
        output_path = os.path.join(self.temp_dir, f'formatted_pdf_{uuid.uuid4().hex}.pdf')
        
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Title style
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        # Heading style
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20
        )
        
        # Body style
        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            leading=14,
            spaceAfter=12,
            alignment=0  # Left alignment
        )
        
        # Build story
        story = []
        
        # Add title
        if title:
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 20))
        
        # Process content
        lines = content.split('\n')
        current_paragraph = []
        
        for line in lines:
            line = line.strip()
            
            if not line:  # Empty line
                if current_paragraph:
                    para_text = ' '.join(current_paragraph)
                    story.append(Paragraph(para_text, body_style))
                    current_paragraph = []
                story.append(Spacer(1, 6))
            elif line.startswith('#'):  # Heading
                if current_paragraph:
                    para_text = ' '.join(current_paragraph)
                    story.append(Paragraph(para_text, body_style))
                    current_paragraph = []
                
                heading_text = line.lstrip('#').strip()
                story.append(Paragraph(heading_text, heading_style))
            else:
                current_paragraph.append(line)
        
        # Add remaining paragraph
        if current_paragraph:
            para_text = ' '.join(current_paragraph)
            story.append(Paragraph(para_text, body_style))
        
        # Build PDF
        doc.build(story)
        
        # Set metadata
        if author or subject:
            self._set_pdf_metadata(output_path, title, author, subject)
        
        return output_path
    
    def _set_pdf_metadata(self, pdf_path, title="", author="", subject=""):
        """Set PDF metadata"""
        try:
            pdf_doc = fitz.open(pdf_path)
            metadata = pdf_doc.metadata
            
            if title:
                metadata['title'] = title
            if author:
                metadata['author'] = author
            if subject:
                metadata['subject'] = subject
            
            pdf_doc.set_metadata(metadata)
            pdf_doc.save(pdf_path, incremental=True)
            pdf_doc.close()
        except Exception as e:
            print(f"Error setting metadata: {str(e)}")
    
    def create_multi_column_pdf(self, text, columns=2, font_size=10):
        """Create multi-column PDF layout"""
        
        output_path = os.path.join(self.temp_dir, f'multi_column_{uuid.uuid4().hex}.pdf')
        
        # Use canvas for more control over layout
        c = canvas.Canvas(output_path, pagesize=A4)
        page_width, page_height = A4
        
        # Calculate column dimensions
        margin = 72
        usable_width = page_width - 2 * margin
        column_width = (usable_width - (columns - 1) * 20) / columns  # 20pt gap between columns
        
        # Split text into words
        words = text.split()
        
        # Current position
        current_column = 0
        current_y = page_height - margin
        
        # Font settings
        c.setFont("Helvetica", font_size)
        line_height = font_size * 1.2
        
        # Process words
        current_line = []
        
        for word in words:
            # Check if adding this word would exceed column width
            test_line = ' '.join(current_line + [word])
            text_width = c.stringWidth(test_line, "Helvetica", font_size)
            
            if text_width <= column_width:
                current_line.append(word)
            else:
                # Draw current line
                if current_line:
                    line_text = ' '.join(current_line)
                    x = margin + current_column * (column_width + 20)
                    c.drawString(x, current_y, line_text)
                    current_y -= line_height
                
                # Start new line with current word
                current_line = [word]
                
                # Check if we need to move to next column or page
                if current_y < margin + line_height:
                    current_column += 1
                    if current_column >= columns:
                        # New page
                        c.showPage()
                        c.setFont("Helvetica", font_size)
                        current_column = 0
                        current_y = page_height - margin
                    else:
                        # Reset to top of next column
                        current_y = page_height - margin
        
        # Draw remaining line
        if current_line:
            line_text = ' '.join(current_line)
            x = margin + current_column * (column_width + 20)
            c.drawString(x, current_y, line_text)
        
        c.save()
        return output_path
    
    def create_table_pdf(self, data, headers=None):
        """Create PDF with table data"""
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib import colors
        
        output_path = os.path.join(self.temp_dir, f'table_pdf_{uuid.uuid4().hex}.pdf')
        
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        
        # Prepare table data
        table_data = []
        if headers:
            table_data.append(headers)
        
        table_data.extend(data)
        
        # Create table
        table = Table(table_data)
        
        # Style the table
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])
        
        table.setStyle(style)
        
        # Build PDF
        story = [table]
        doc.build(story)
        
        return output_path
