# PDF2Any - Complete PDF Processing Suite 🎯

A comprehensive web application for PDF processing with **25+ advanced tools**, **interactive PDF viewer**, and **multilingual support**.

## 🌟 Key Features

### 📄 **25 PDF Tools Available**

#### **Basic Tools (8)**
- ✅ Add Page Numbers - Customizable numbering
- ✅ Merge PDFs - Combine multiple files
- ✅ Protect PDF - Password encryption
- ✅ Compress PDF - Size optimization
- ✅ Remove Pages - Delete specific pages
- ✅ Split PDF - Extract page ranges
- ✅ Extract Pages - Create new PDFs
- ✅ Extract Images - Get all images

#### **Conversion Tools (8)**
- ✅ Text to PDF - Rich formatting
- ✅ Rotate PDF - 90°/180°/270° rotation
- ✅ Unlock PDF - Remove passwords
- ✅ Add Watermark - **Enhanced with interactive placement**
- ✅ PDF to Image - High-quality conversion
- ✅ Image to PDF - Multiple formats
- ✅ PNG ⟷ PDF - Bidirectional conversion
- ✅ PDF to OCR - **Advanced text recognition**

#### **Advanced Tools (9)**
- ✅ Translate PDF - Multi-language support
- ✅ Sign PDF - **Enhanced with drawing/text/image signatures**
- ✅ Crop PDF - Interactive cropping
- ✅ Organize PDF - **Enhanced with drag-and-drop**
- ✅ GIF ⟷ PDF - Animation support
- ✅ Generate QR Code - Custom QR creation
- ✅ Edit PDF - Text and image editing
- ✅ Extract Text - Smart text extraction
- ✅ Optimize PDF - Performance enhancement

### 🎨 **Interactive PDF Viewer (PDF.js)**
- 📱 **Responsive design** for all devices
- 🔍 **Zoom controls** with smooth scaling
- 📑 **Page thumbnails** with navigation
- 👆 **Click-to-select** areas for tools
- 🖱️ **Drag-and-drop** page reordering
- 📱 **Touch-friendly** mobile interface
- 🎯 **Real-time preview** of modifications

### 🌐 **Multilingual Support**
- 🇸🇦 **Arabic** (RTL layout)
- 🇺🇸 **English** (LTR layout)
- 🔄 **Instant switching** between languages

### 🔧 **Advanced OCR Engine**
- 📝 **Text extraction** with confidence scoring
- 🌍 **Multi-language** OCR support (15+ languages)
- 🔍 **Document layout** analysis
- 📄 **Searchable PDF** creation
- 🖼️ **Image enhancement** for better recognition

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip package manager

### Installation
```bash
# Clone repository
git clone <repository-url>
cd pdf2any

# Install dependencies
pip install -r requirements.txt

# Run application
python run.py

# Open browser
http://localhost:5000
```

## 📦 Core Dependencies

```
Flask==2.3.3              # Web framework
PyMuPDF==1.23.8           # PDF processing
PyPDF2==3.0.1             # PDF manipulation
reportlab==4.0.4          # PDF generation
Pillow==10.0.1            # Image processing
pytesseract==0.3.10       # OCR engine
opencv-python==********   # Computer vision
pdf2image==1.16.3         # PDF to image
qrcode==7.4.2             # QR code generation
cryptography==41.0.7      # PDF encryption
```

## 🛠️ Architecture

```
pdf2any/
├── app.py                    # Main Flask application
├── run.py                    # Application runner
├── requirements.txt          # Dependencies
├── utils/                    # Processing modules
│   ├── pdf_processor.py      # Core PDF operations
│   ├── ocr_processor.py      # Advanced OCR functionality
│   ├── watermark_processor.py # Watermark processing
│   ├── signature_processor.py # Digital signatures
│   └── ...                   # Other processors
├── templates/                # HTML templates
│   ├── base.html            # Base template
│   ├── index.html           # Homepage
│   └── tools/               # Tool-specific templates
├── static/                  # Static assets
│   ├── js/pdf-viewer.js     # Interactive PDF viewer
│   ├── css/                 # Stylesheets
│   └── images/              # Images
└── translations/            # Language files
```

## 🎯 Enhanced Tools Usage

### **Interactive Watermark Tool**
1. Upload PDF file
2. Choose watermark type (text/image/stamp)
3. **Click on PDF** to place watermark interactively
4. Adjust settings with real-time preview
5. Apply and download

### **Advanced Signature Tool**
1. Upload PDF document
2. Create signature:
   - **Draw** with mouse/touch
   - **Type** with custom fonts
   - **Upload** image signature
3. **Click on PDF** to place signature
4. Add date/time/location if needed
5. Apply and download

### **Interactive Organize Tool**
1. Upload PDF file
2. View page thumbnails
3. **Drag and drop** to reorder pages
4. **Right-click** for context menu (duplicate/delete/rotate)
5. Apply changes and download

### **Advanced OCR Processing**
1. Upload scanned PDF
2. Select recognition language
3. Choose enhancement options
4. Get extracted text with confidence scores
5. Download searchable PDF

## 🌍 Supported Languages

### **Interface Languages**
- العربية (Arabic) - Full RTL support
- English - Complete localization

### **OCR Languages**
- English, Arabic, French, German, Spanish
- Chinese (Simplified/Traditional)
- Japanese, Korean, Russian
- Italian, Portuguese, Dutch
- And many more...

## 📱 Mobile & Responsive

- ✅ **Touch gestures** for PDF interaction
- ✅ **Mobile-optimized** interface
- ✅ **Responsive layouts** for all screen sizes
- ✅ **Drag-and-drop** on touch devices
- ✅ **Swipe navigation** for pages

## 🔒 Security & Privacy

- 🛡️ **Client-side processing** - Files stay on your device
- 🔐 **No server storage** - Files are not saved
- 🔒 **Secure encryption** for password protection
- 🌐 **HTTPS ready** for secure connections
- 🛡️ **CORS protection** enabled

## 🚀 Performance Features

- ⚡ **Efficient PDF processing** with PyMuPDF
- 🎨 **Client-side rendering** with PDF.js
- 🖼️ **Optimized image processing**
- 💾 **Memory-efficient** operations
- 📊 **Progress indicators** for long operations
- 🔄 **Async processing** for better UX

## 🎨 UI/UX Features

- 🎨 **Modern design** with Tailwind CSS
- 🌙 **Consistent theming** across all tools
- 📱 **Responsive components**
- 🎯 **Intuitive navigation**
- 💫 **Smooth animations** and transitions
- 🔔 **User feedback** and notifications

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/AmazingFeature`
3. Commit changes: `git commit -m 'Add AmazingFeature'`
4. Push to branch: `git push origin feature/AmazingFeature`
5. Open Pull Request

## 📄 License

This project is licensed under the **MIT License** - see the LICENSE file for details.

## 🙏 Acknowledgments

- **PDF.js team** for excellent PDF rendering
- **Tesseract OCR** for text recognition capabilities
- **Flask community** for the robust web framework
- **Open source contributors** worldwide

## 📞 Support

- 🐛 **Issues**: Create an issue on GitHub
- 📚 **Documentation**: Check the code examples
- 💬 **Questions**: Review the README and code

---

**PDF2Any** - Your complete PDF processing solution! 🎯

*Built with ❤️ for the PDF processing community*
