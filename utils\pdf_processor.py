import fitz  # PyMuPDF
import PyPDF2
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.colors import black, red, green, blue
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import tempfile
import zipfile
from PIL import Image
import io
import uuid
from datetime import datetime

class PDFProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        
    def merge_pdfs(self, files):
        """Merge multiple PDF files into one"""
        try:
            if not files or len(files) < 2:
                raise ValueError("At least 2 files required for merging")

            merged_pdf = fitz.open()

            for i, file in enumerate(files):
                if not file or not file.filename:
                    continue

                if not file.filename.lower().endswith('.pdf'):
                    continue

                try:
                    # Reset file pointer
                    file.seek(0)
                    pdf_bytes = file.read()

                    if not pdf_bytes:
                        continue

                    pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")

                    if pdf_doc.page_count == 0:
                        pdf_doc.close()
                        continue

                    merged_pdf.insert_pdf(pdf_doc)
                    pdf_doc.close()

                except Exception as e:
                    print(f"Error processing file {i+1}: {str(e)}")
                    continue

            if merged_pdf.page_count == 0:
                merged_pdf.close()
                raise ValueError("No valid PDF pages found to merge")

            output_path = os.path.join(self.temp_dir, f'merged_{uuid.uuid4().hex}.pdf')
            merged_pdf.save(output_path)
            merged_pdf.close()

            return output_path

        except Exception as e:
            raise Exception(f"Error merging PDFs: {str(e)}")
    
    def split_pdf(self, file):
        """Split PDF into individual pages"""
        try:
            if not file:
                raise ValueError("No file provided")

            # Reset file pointer
            file.seek(0)
            pdf_bytes = file.read()

            if not pdf_bytes:
                raise ValueError("Empty file provided")

            pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")

            if pdf_doc.page_count == 0:
                pdf_doc.close()
                raise ValueError("PDF has no pages")

            result_paths = []
            for page_num in range(pdf_doc.page_count):
                try:
                    new_pdf = fitz.open()
                    new_pdf.insert_pdf(pdf_doc, from_page=page_num, to_page=page_num)

                    output_path = os.path.join(self.temp_dir, f'page_{page_num + 1}_{uuid.uuid4().hex}.pdf')
                    new_pdf.save(output_path)
                    new_pdf.close()
                    result_paths.append(output_path)

                except Exception as e:
                    print(f"Error splitting page {page_num + 1}: {str(e)}")
                    continue

            pdf_doc.close()

            if not result_paths:
                raise ValueError("No pages could be split")

            return result_paths

        except Exception as e:
            raise Exception(f"Error splitting PDF: {str(e)}")
    
    def compress_pdf(self, file, quality='medium'):
        """Compress PDF file"""
        try:
            if not file:
                raise ValueError("No file provided")

            # Reset file pointer
            file.seek(0)
            pdf_bytes = file.read()

            if not pdf_bytes:
                raise ValueError("Empty file provided")

            pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")

            if pdf_doc.page_count == 0:
                pdf_doc.close()
                raise ValueError("PDF has no pages")

            # Compression settings based on quality
            if quality == 'high':
                deflate_level = 1
                image_quality = 95
            elif quality == 'medium':
                deflate_level = 6
                image_quality = 75
            else:  # low
                deflate_level = 9
                image_quality = 50

            output_path = os.path.join(self.temp_dir, f'compressed_{uuid.uuid4().hex}.pdf')

            # Save with compression
            pdf_doc.save(
                output_path,
                deflate=True,
                deflate_level=deflate_level,
                clean=True,
                garbage=4
            )
            pdf_doc.close()

            return output_path

        except Exception as e:
            raise Exception(f"Error compressing PDF: {str(e)}")
    
    def protect_pdf(self, file, password):
        """Add password protection to PDF"""
        try:
            if not file:
                raise ValueError("No file provided")

            if not password:
                raise ValueError("Password is required")

            # Reset file pointer
            file.seek(0)
            pdf_bytes = file.read()

            if not pdf_bytes:
                raise ValueError("Empty file provided")

            pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")

            if pdf_doc.page_count == 0:
                pdf_doc.close()
                raise ValueError("PDF has no pages")

            output_path = os.path.join(self.temp_dir, f'protected_{uuid.uuid4().hex}.pdf')

            # Set permissions and password
            perm = int(
                fitz.PDF_PERM_ACCESSIBILITY |
                fitz.PDF_PERM_PRINT |
                fitz.PDF_PERM_COPY |
                fitz.PDF_PERM_ANNOTATE
            )

            pdf_doc.save(
                output_path,
                encryption=fitz.PDF_ENCRYPT_AES_256,
                owner_pw=password,
                user_pw=password,
                permissions=perm
            )
            pdf_doc.close()

            return output_path

        except Exception as e:
            raise Exception(f"Error protecting PDF: {str(e)}")
    
    def add_page_numbers(self, file, position='bottom-right', start_page=1):
        """Add page numbers to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            page_rect = page.rect
            
            # Calculate position
            if position == 'bottom-right':
                x = page_rect.width - 50
                y = page_rect.height - 20
            elif position == 'bottom-left':
                x = 50
                y = page_rect.height - 20
            elif position == 'top-right':
                x = page_rect.width - 50
                y = 30
            else:  # top-left
                x = 50
                y = 30
            
            # Insert page number
            page_number = start_page + page_num
            text = str(page_number)
            
            page.insert_text((x, y), text, fontsize=12, color=(0, 0, 0))
        
        output_path = os.path.join(self.temp_dir, f'numbered_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def remove_pages(self, file, pages_to_remove):
        """Remove specific pages from PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Convert to 0-based indexing and sort in reverse order
        pages_to_remove = sorted([int(p) - 1 for p in pages_to_remove], reverse=True)
        
        for page_num in pages_to_remove:
            if 0 <= page_num < pdf_doc.page_count:
                pdf_doc.delete_page(page_num)
        
        output_path = os.path.join(self.temp_dir, f'removed_pages_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def extract_pages(self, file, pages_to_extract):
        """Extract specific pages from PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        new_pdf = fitz.open()
        
        for page_num in pages_to_extract:
            page_index = int(page_num) - 1  # Convert to 0-based
            if 0 <= page_index < pdf_doc.page_count:
                new_pdf.insert_pdf(pdf_doc, from_page=page_index, to_page=page_index)
        
        output_path = os.path.join(self.temp_dir, f'extracted_{uuid.uuid4().hex}.pdf')
        new_pdf.save(output_path)
        new_pdf.close()
        pdf_doc.close()
        
        return output_path
    
    def rotate_pdf(self, file, rotation_angle):
        """Rotate PDF pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for page in pdf_doc:
            page.set_rotation(rotation_angle)
        
        output_path = os.path.join(self.temp_dir, f'rotated_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def unlock_pdf(self, file, password):
        """Remove password protection from PDF"""
        pdf_bytes = file.read()
        
        try:
            pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            
            if pdf_doc.needs_pass:
                if not pdf_doc.authenticate(password):
                    raise Exception("Invalid password")
            
            output_path = os.path.join(self.temp_dir, f'unlocked_{uuid.uuid4().hex}.pdf')
            pdf_doc.save(output_path)
            pdf_doc.close()
            
            return output_path
        except Exception as e:
            raise Exception(f"Failed to unlock PDF: {str(e)}")
    
    def create_zip(self, file_paths, zip_name):
        """Create a zip file containing multiple files"""
        zip_path = os.path.join(self.temp_dir, zip_name)
        
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for i, file_path in enumerate(file_paths):
                filename = f"page_{i+1}.pdf"
                zipf.write(file_path, filename)
        
        return zip_path
