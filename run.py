#!/usr/bin/env python3
"""
PDF2Any - Professional PDF Processing Web Application
Run this script to start the development server
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all required packages are installed"""
    try:
        import flask
        import fitz
        import PyPDF2
        import reportlab
        import PIL
        import qrcode
        import requests
        print("✓ All required packages are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing required package: {e}")
        print("Please install requirements with: pip install -r requirements.txt")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        'uploads',
        'processed', 
        'static/temp',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def main():
    """Main function to start the application"""
    print("=" * 50)
    print("🚀 Starting PDF2Any Application")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("✗ app.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    print("\n" + "=" * 50)
    print("🌟 PDF2Any Features:")
    print("=" * 50)
    print("• 25 Professional PDF Tools")
    print("• Bilingual Support (English/Arabic)")
    print("• Modern Responsive UI")
    print("• Advanced Watermarking")
    print("• PDF Translation with Google Translate")
    print("• Image Processing & Conversion")
    print("• Text to PDF Conversion")
    print("• OCR Text Extraction")
    print("• And much more!")
    
    print("\n" + "=" * 50)
    print("🔧 Starting Development Server...")
    print("=" * 50)
    print("Server will be available at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the Flask application
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n✓ Server stopped successfully")
    except Exception as e:
        print(f"\n✗ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
