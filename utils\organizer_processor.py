import fitz  # PyMuPDF
from PIL import Image
import io
import os
import tempfile
import uuid
import json
import base64

class OrganizerProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def get_pdf_thumbnails(self, file, max_width=200, max_height=280):
        """Generate thumbnails for all PDF pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        thumbnails = []
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            
            # Calculate scale to fit within max dimensions
            page_rect = page.rect
            scale_x = max_width / page_rect.width
            scale_y = max_height / page_rect.height
            scale = min(scale_x, scale_y, 1.0)  # Don't upscale
            
            # Create matrix for scaling
            mat = fitz.Matrix(scale, scale)
            pix = page.get_pixmap(matrix=mat)
            
            # Convert to PIL Image
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            
            # Save thumbnail
            thumb_path = os.path.join(self.temp_dir, f'thumb_{page_num}_{uuid.uuid4().hex}.png')
            img.save(thumb_path, optimize=True)
            
            # Convert to base64 for web display
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG', optimize=True)
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            
            thumbnails.append({
                'page_number': page_num + 1,
                'original_index': page_num,
                'thumbnail_path': thumb_path,
                'thumbnail_base64': f"data:image/png;base64,{img_base64}",
                'width': img.width,
                'height': img.height,
                'rotation': 0,  # Current rotation
                'selected': False
            })
        
        pdf_doc.close()
        return thumbnails
    
    def reorganize_pdf(self, file, page_order, rotations=None, deletions=None):
        """Reorganize PDF pages according to new order"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Create new PDF
        new_pdf = fitz.open()
        
        # Process each page in the new order
        for new_index, original_index in enumerate(page_order):
            if deletions and original_index in deletions:
                continue  # Skip deleted pages
            
            if original_index < pdf_doc.page_count:
                # Get the original page
                original_page = pdf_doc[original_index]
                
                # Insert page into new PDF
                new_pdf.insert_pdf(pdf_doc, from_page=original_index, to_page=original_index)
                
                # Apply rotation if specified
                if rotations and str(original_index) in rotations:
                    rotation = rotations[str(original_index)]
                    if rotation != 0:
                        new_page = new_pdf[new_pdf.page_count - 1]
                        new_page.set_rotation(rotation)
        
        # Save reorganized PDF
        output_path = os.path.join(self.temp_dir, f'reorganized_{uuid.uuid4().hex}.pdf')
        new_pdf.save(output_path)
        new_pdf.close()
        pdf_doc.close()
        
        return output_path
    
    def rotate_pages(self, file, rotations):
        """Rotate specific pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        for page_index, rotation in rotations.items():
            page_num = int(page_index)
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                current_rotation = page.rotation
                new_rotation = (current_rotation + rotation) % 360
                page.set_rotation(new_rotation)
        
        output_path = os.path.join(self.temp_dir, f'rotated_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def delete_pages(self, file, pages_to_delete):
        """Delete specific pages from PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Sort pages in reverse order to avoid index shifting
        pages_to_delete = sorted(pages_to_delete, reverse=True)
        
        for page_num in pages_to_delete:
            if 0 <= page_num < pdf_doc.page_count:
                pdf_doc.delete_page(page_num)
        
        output_path = os.path.join(self.temp_dir, f'deleted_pages_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def duplicate_pages(self, file, pages_to_duplicate):
        """Duplicate specific pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Process duplications from end to beginning to avoid index issues
        for page_num in sorted(pages_to_duplicate, reverse=True):
            if 0 <= page_num < pdf_doc.page_count:
                # Insert a copy of the page right after the original
                pdf_doc.insert_pdf(pdf_doc, from_page=page_num, to_page=page_num, start_at=page_num + 1)
        
        output_path = os.path.join(self.temp_dir, f'duplicated_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def extract_page_range(self, file, start_page, end_page):
        """Extract a range of pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        new_pdf = fitz.open()
        
        # Ensure valid range
        start_page = max(0, start_page - 1)  # Convert to 0-based
        end_page = min(pdf_doc.page_count - 1, end_page - 1)  # Convert to 0-based
        
        # Extract pages
        for page_num in range(start_page, end_page + 1):
            new_pdf.insert_pdf(pdf_doc, from_page=page_num, to_page=page_num)
        
        output_path = os.path.join(self.temp_dir, f'extracted_range_{uuid.uuid4().hex}.pdf')
        new_pdf.save(output_path)
        new_pdf.close()
        pdf_doc.close()
        
        return output_path
    
    def reverse_pages(self, file):
        """Reverse the order of all pages"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        new_pdf = fitz.open()
        
        # Insert pages in reverse order
        for page_num in range(pdf_doc.page_count - 1, -1, -1):
            new_pdf.insert_pdf(pdf_doc, from_page=page_num, to_page=page_num)
        
        output_path = os.path.join(self.temp_dir, f'reversed_{uuid.uuid4().hex}.pdf')
        new_pdf.save(output_path)
        new_pdf.close()
        pdf_doc.close()
        
        return output_path
    
    def insert_blank_pages(self, file, positions, page_size=None):
        """Insert blank pages at specified positions"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Get page size from first page if not specified
        if page_size is None and pdf_doc.page_count > 0:
            first_page = pdf_doc[0]
            page_size = (first_page.rect.width, first_page.rect.height)
        elif page_size is None:
            page_size = (595, 842)  # A4 default
        
        # Sort positions in reverse order to avoid index shifting
        positions = sorted(positions, reverse=True)
        
        for position in positions:
            if 0 <= position <= pdf_doc.page_count:
                # Create blank page
                blank_page = pdf_doc.new_page(width=page_size[0], height=page_size[1])
                
                # Move the blank page to the correct position
                if position < pdf_doc.page_count - 1:
                    pdf_doc.move_page(pdf_doc.page_count - 1, position)
        
        output_path = os.path.join(self.temp_dir, f'blank_inserted_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def get_page_info(self, file):
        """Get detailed information about each page"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        pages_info = []
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            page_rect = page.rect
            
            # Get text content
            text = page.get_text()
            word_count = len(text.split()) if text else 0
            
            # Get images
            image_list = page.get_images()
            image_count = len(image_list)
            
            # Get annotations
            annotations = page.annots()
            annotation_count = len(list(annotations))
            
            pages_info.append({
                'page_number': page_num + 1,
                'width': page_rect.width,
                'height': page_rect.height,
                'rotation': page.rotation,
                'word_count': word_count,
                'image_count': image_count,
                'annotation_count': annotation_count,
                'has_text': bool(text.strip()),
                'orientation': 'landscape' if page_rect.width > page_rect.height else 'portrait'
            })
        
        pdf_doc.close()
        return pages_info
    
    def optimize_page_order(self, file, criteria='size'):
        """Automatically optimize page order based on criteria"""
        pages_info = self.get_page_info(file)
        
        if criteria == 'size':
            # Sort by page size (area)
            sorted_pages = sorted(pages_info, key=lambda x: x['width'] * x['height'])
        elif criteria == 'orientation':
            # Group by orientation
            portrait_pages = [p for p in pages_info if p['orientation'] == 'portrait']
            landscape_pages = [p for p in pages_info if p['orientation'] == 'landscape']
            sorted_pages = portrait_pages + landscape_pages
        elif criteria == 'content':
            # Sort by content richness (text + images)
            sorted_pages = sorted(pages_info, key=lambda x: x['word_count'] + x['image_count'], reverse=True)
        else:
            sorted_pages = pages_info
        
        # Extract the new order
        new_order = [p['page_number'] - 1 for p in sorted_pages]  # Convert to 0-based
        
        return self.reorganize_pdf(file, new_order)
