#!/bin/bash

# PDF2Any - Professional PDF Processing Web Application
# Startup script for Linux/macOS

echo "================================================"
echo "🚀 Starting PDF2Any Application"
echo "================================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "Please install Python 3.7 or higher"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    echo "❌ app.py not found. Please run this script from the project root directory."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source venv/bin/activate

# Install/update requirements
echo "📦 Installing/updating requirements..."
pip install -r requirements.txt

echo
echo "================================================"
echo "🌟 PDF2Any Features:"
echo "================================================"
echo "• 25 Professional PDF Tools"
echo "• Bilingual Support (English/Arabic)"
echo "• Modern Responsive UI"
echo "• Advanced Watermarking"
echo "• PDF Translation with Google Translate"
echo "• Image Processing & Conversion"
echo "• Text to PDF Conversion"
echo "• OCR Text Extraction"
echo "• And much more!"
echo

echo "================================================"
echo "🔧 Starting Development Server..."
echo "================================================"
echo "Server will be available at: http://localhost:5000"
echo "Press Ctrl+C to stop the server"
echo "================================================"
echo

# Start the application
python run.py

echo
echo "✅ Server stopped successfully"
