{% extends "base.html" %}

{% block title %}Generate QR Code - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-gray-500 to-black rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-qrcode text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Generate QR Code</h1>
        <p class="text-xl text-gray-600">Create custom QR codes and convert them to PDF format</p>
    </div>
    
    <!-- QR Code Generator -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">QR Code Settings</h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Left Panel - Settings -->
            <div class="space-y-6">
                <!-- QR Code Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">QR Code Type</label>
                    <select id="qrType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        <option value="text">Plain Text</option>
                        <option value="url">Website URL</option>
                        <option value="email">Email Address</option>
                        <option value="phone">Phone Number</option>
                        <option value="sms">SMS Message</option>
                        <option value="wifi">WiFi Network</option>
                        <option value="vcard">Contact Card (vCard)</option>
                        <option value="location">GPS Location</option>
                    </select>
                </div>
                
                <!-- Content Input -->
                <div id="contentInputs">
                    <!-- Text Input (Default) -->
                    <div id="textInput">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Text Content</label>
                        <textarea id="qrText" rows="4" placeholder="Enter your text here..." 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"></textarea>
                    </div>
                    
                    <!-- URL Input -->
                    <div id="urlInput" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Website URL</label>
                        <input type="url" id="qrUrl" placeholder="https://example.com" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                    </div>
                    
                    <!-- Email Input -->
                    <div id="emailInput" class="hidden space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" id="qrEmail" placeholder="<EMAIL>" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Subject (Optional)</label>
                            <input type="text" id="emailSubject" placeholder="Email subject" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Message (Optional)</label>
                            <textarea id="emailMessage" rows="3" placeholder="Email message" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"></textarea>
                        </div>
                    </div>
                    
                    <!-- Phone Input -->
                    <div id="phoneInput" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="qrPhone" placeholder="+1234567890" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                    </div>
                    
                    <!-- SMS Input -->
                    <div id="smsInput" class="hidden space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="smsPhone" placeholder="+1234567890" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea id="smsMessage" rows="3" placeholder="SMS message" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"></textarea>
                        </div>
                    </div>
                    
                    <!-- WiFi Input -->
                    <div id="wifiInput" class="hidden space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Network Name (SSID)</label>
                            <input type="text" id="wifiSSID" placeholder="WiFi Network Name" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" id="wifiPassword" placeholder="WiFi Password" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Security Type</label>
                            <select id="wifiSecurity" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                                <option value="WPA">WPA/WPA2</option>
                                <option value="WEP">WEP</option>
                                <option value="nopass">No Password</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- vCard Input -->
                    <div id="vcardInput" class="hidden space-y-3">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" id="vcardFirstName" placeholder="John" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" id="vcardLastName" placeholder="Doe" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Organization</label>
                            <input type="text" id="vcardOrg" placeholder="Company Name" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                            <input type="tel" id="vcardPhone" placeholder="+1234567890" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="vcardEmail" placeholder="<EMAIL>" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Website</label>
                            <input type="url" id="vcardUrl" placeholder="https://example.com" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                        </div>
                    </div>
                    
                    <!-- Location Input -->
                    <div id="locationInput" class="hidden space-y-3">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
                                <input type="number" id="locationLat" step="any" placeholder="40.7128" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
                                <input type="number" id="locationLng" step="any" placeholder="-74.0060" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- QR Code Customization -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Customization</h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                            <select id="qrSize" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                                <option value="200">Small (200x200)</option>
                                <option value="300" selected>Medium (300x300)</option>
                                <option value="400">Large (400x400)</option>
                                <option value="500">Extra Large (500x500)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Error Correction</label>
                            <select id="errorCorrection" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500">
                                <option value="L">Low (~7%)</option>
                                <option value="M" selected>Medium (~15%)</option>
                                <option value="Q">Quartile (~25%)</option>
                                <option value="H">High (~30%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Foreground Color</label>
                            <input type="color" id="foregroundColor" value="#000000" 
                                   class="w-full h-10 border border-gray-300 rounded-lg">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                            <input type="color" id="backgroundColor" value="#ffffff" 
                                   class="w-full h-10 border border-gray-300 rounded-lg">
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex space-x-4">
                    <button id="generateBtn" class="flex-1 btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-qrcode mr-2"></i>Generate QR Code
                    </button>
                    <button id="downloadPdfBtn" class="flex-1 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50" disabled>
                        <i class="fas fa-file-pdf mr-2"></i>Download PDF
                    </button>
                </div>
            </div>
            
            <!-- Right Panel - Preview -->
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">QR Code Preview</h3>
                    <div id="qrPreview" class="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center min-h-80 flex items-center justify-center">
                        <div class="text-gray-500">
                            <i class="fas fa-qrcode text-6xl mb-4"></i>
                            <p>QR code will appear here</p>
                        </div>
                    </div>
                </div>
                
                <!-- QR Code Info -->
                <div id="qrInfo" class="bg-gray-50 rounded-lg p-4 hidden">
                    <h4 class="font-semibold text-gray-800 mb-2">QR Code Information</h4>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>Type: <span id="infoType">-</span></div>
                        <div>Size: <span id="infoSize">-</span></div>
                        <div>Error Correction: <span id="infoErrorCorrection">-</span></div>
                        <div>Data Length: <span id="infoDataLength">-</span> characters</div>
                    </div>
                </div>
                
                <!-- Download Options -->
                <div id="downloadOptions" class="hidden">
                    <h4 class="font-semibold text-gray-800 mb-3">Download Options</h4>
                    <div class="space-y-2">
                        <button id="downloadPngBtn" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-image mr-2"></i>Download PNG
                        </button>
                        <button id="downloadJpgBtn" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-image mr-2"></i>Download JPG
                        </button>
                        <button id="downloadSvgBtn" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-vector-square mr-2"></i>Download SVG
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-gray-50 rounded-xl p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to create QR codes:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-gray-700">
            <li>Select the type of content you want to encode</li>
            <li>Fill in the required information for your chosen type</li>
            <li>Customize the appearance (size, colors, error correction)</li>
            <li>Click "Generate QR Code" to create your QR code</li>
            <li>Download as PNG, JPG, SVG, or PDF format</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tips:</strong>
                <br>• Higher error correction allows the QR code to work even if partially damaged
                <br>• Larger sizes are better for printing and scanning from distance
                <br>• Test your QR code with different devices before final use
                <br>• Keep URLs short for better scanning reliability
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
let currentQRCode = null;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // QR Type change
    document.getElementById('qrType').addEventListener('change', handleTypeChange);
    
    // Generate button
    document.getElementById('generateBtn').addEventListener('click', generateQRCode);
    
    // Download buttons
    document.getElementById('downloadPdfBtn').addEventListener('click', downloadPDF);
    document.getElementById('downloadPngBtn').addEventListener('click', () => downloadImage('png'));
    document.getElementById('downloadJpgBtn').addEventListener('click', () => downloadImage('jpg'));
    document.getElementById('downloadSvgBtn').addEventListener('click', () => downloadImage('svg'));
    
    // Auto-generate on input change
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        if (input.id !== 'qrType') {
            input.addEventListener('input', debounce(autoGenerate, 500));
        }
    });
}

function handleTypeChange() {
    const type = document.getElementById('qrType').value;
    
    // Hide all input sections
    document.querySelectorAll('#contentInputs > div').forEach(div => {
        div.classList.add('hidden');
    });
    
    // Show relevant input section
    document.getElementById(type + 'Input').classList.remove('hidden');
    
    // Clear preview
    clearPreview();
}

function generateQRCode() {
    const type = document.getElementById('qrType').value;
    const data = getQRData(type);
    
    if (!data) {
        alert('Please fill in the required information.');
        return;
    }
    
    const size = parseInt(document.getElementById('qrSize').value);
    const errorCorrectionLevel = document.getElementById('errorCorrection').value;
    const foregroundColor = document.getElementById('foregroundColor').value;
    const backgroundColor = document.getElementById('backgroundColor').value;
    
    const options = {
        width: size,
        height: size,
        errorCorrectionLevel: errorCorrectionLevel,
        color: {
            dark: foregroundColor,
            light: backgroundColor
        }
    };
    
    // Generate QR code
    const canvas = document.createElement('canvas');
    QRCode.toCanvas(canvas, data, options, function(error) {
        if (error) {
            alert('Error generating QR code: ' + error.message);
            return;
        }
        
        // Display preview
        const preview = document.getElementById('qrPreview');
        preview.innerHTML = '';
        preview.appendChild(canvas);
        
        // Update info
        updateQRInfo(type, size, errorCorrectionLevel, data);
        
        // Enable download buttons
        document.getElementById('downloadPdfBtn').disabled = false;
        document.getElementById('downloadOptions').classList.remove('hidden');
        
        // Store current QR code
        currentQRCode = { canvas, data, options };
    });
}

function getQRData(type) {
    switch (type) {
        case 'text':
            return document.getElementById('qrText').value.trim();
            
        case 'url':
            const url = document.getElementById('qrUrl').value.trim();
            return url.startsWith('http') ? url : 'https://' + url;
            
        case 'email':
            const email = document.getElementById('qrEmail').value.trim();
            const subject = document.getElementById('emailSubject').value.trim();
            const message = document.getElementById('emailMessage').value.trim();
            if (!email) return null;
            
            let emailData = `mailto:${email}`;
            const params = [];
            if (subject) params.push(`subject=${encodeURIComponent(subject)}`);
            if (message) params.push(`body=${encodeURIComponent(message)}`);
            if (params.length > 0) emailData += '?' + params.join('&');
            return emailData;
            
        case 'phone':
            const phone = document.getElementById('qrPhone').value.trim();
            return phone ? `tel:${phone}` : null;
            
        case 'sms':
            const smsPhone = document.getElementById('smsPhone').value.trim();
            const smsMessage = document.getElementById('smsMessage').value.trim();
            if (!smsPhone) return null;
            return `sms:${smsPhone}${smsMessage ? `?body=${encodeURIComponent(smsMessage)}` : ''}`;
            
        case 'wifi':
            const ssid = document.getElementById('wifiSSID').value.trim();
            const password = document.getElementById('wifiPassword').value.trim();
            const security = document.getElementById('wifiSecurity').value;
            if (!ssid) return null;
            return `WIFI:T:${security};S:${ssid};P:${password};H:false;;`;
            
        case 'vcard':
            const firstName = document.getElementById('vcardFirstName').value.trim();
            const lastName = document.getElementById('vcardLastName').value.trim();
            const org = document.getElementById('vcardOrg').value.trim();
            const vcardPhone = document.getElementById('vcardPhone').value.trim();
            const vcardEmail = document.getElementById('vcardEmail').value.trim();
            const vcardUrl = document.getElementById('vcardUrl').value.trim();
            
            if (!firstName && !lastName) return null;
            
            let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';
            vcard += `FN:${firstName} ${lastName}\n`;
            vcard += `N:${lastName};${firstName};;;\n`;
            if (org) vcard += `ORG:${org}\n`;
            if (vcardPhone) vcard += `TEL:${vcardPhone}\n`;
            if (vcardEmail) vcard += `EMAIL:${vcardEmail}\n`;
            if (vcardUrl) vcard += `URL:${vcardUrl}\n`;
            vcard += 'END:VCARD';
            return vcard;
            
        case 'location':
            const lat = document.getElementById('locationLat').value.trim();
            const lng = document.getElementById('locationLng').value.trim();
            if (!lat || !lng) return null;
            return `geo:${lat},${lng}`;
            
        default:
            return null;
    }
}

function updateQRInfo(type, size, errorCorrection, data) {
    document.getElementById('infoType').textContent = type.charAt(0).toUpperCase() + type.slice(1);
    document.getElementById('infoSize').textContent = `${size}x${size}px`;
    document.getElementById('infoErrorCorrection').textContent = errorCorrection;
    document.getElementById('infoDataLength').textContent = data.length;
    document.getElementById('qrInfo').classList.remove('hidden');
}

function clearPreview() {
    const preview = document.getElementById('qrPreview');
    preview.innerHTML = `
        <div class="text-gray-500">
            <i class="fas fa-qrcode text-6xl mb-4"></i>
            <p>QR code will appear here</p>
        </div>
    `;
    document.getElementById('qrInfo').classList.add('hidden');
    document.getElementById('downloadOptions').classList.add('hidden');
    document.getElementById('downloadPdfBtn').disabled = true;
    currentQRCode = null;
}

function autoGenerate() {
    const type = document.getElementById('qrType').value;
    const data = getQRData(type);
    if (data) {
        generateQRCode();
    }
}

function downloadImage(format) {
    if (!currentQRCode) {
        alert('Please generate a QR code first.');
        return;
    }
    
    const canvas = currentQRCode.canvas;
    let dataUrl;
    
    if (format === 'svg') {
        // Generate SVG version
        QRCode.toString(currentQRCode.data, {
            type: 'svg',
            ...currentQRCode.options
        }, function(error, svg) {
            if (error) {
                alert('Error generating SVG: ' + error.message);
                return;
            }
            
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'qrcode.svg';
            a.click();
            window.URL.revokeObjectURL(url);
        });
        return;
    }
    
    if (format === 'jpg') {
        // Convert to JPG with white background
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        tempCanvas.width = canvas.width;
        tempCanvas.height = canvas.height;
        
        // Fill with white background
        tempCtx.fillStyle = '#ffffff';
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        
        // Draw QR code on top
        tempCtx.drawImage(canvas, 0, 0);
        
        dataUrl = tempCanvas.toDataURL('image/jpeg', 0.9);
    } else {
        dataUrl = canvas.toDataURL('image/png');
    }
    
    const a = document.createElement('a');
    a.href = dataUrl;
    a.download = `qrcode.${format}`;
    a.click();
}

function downloadPDF() {
    if (!currentQRCode) {
        alert('Please generate a QR code first.');
        return;
    }
    
    const canvas = currentQRCode.canvas;
    const dataUrl = canvas.toDataURL('image/png');
    
    // Send to server to create PDF
    fetch('/api/generate_qr_pdf', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            qr_image: dataUrl,
            qr_data: currentQRCode.data,
            qr_type: document.getElementById('qrType').value
        })
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'qrcode.pdf';
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        alert('Error creating PDF: ' + error.message);
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
