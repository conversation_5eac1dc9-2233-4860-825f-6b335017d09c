{% extends "base.html" %}

{% block title %}PDF to OCR - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-eye text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">PDF to OCR</h1>
        <p class="text-xl text-gray-600">Extract text from scanned PDFs using advanced OCR technology</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a scanned PDF file for OCR processing</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- OCR Settings -->
    <div id="ocrSettings" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">OCR Settings</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Language Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Recognition Language</label>
                <select id="ocrLanguage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="eng">English</option>
                    <option value="ara">Arabic (العربية)</option>
                    <option value="fra">French (Français)</option>
                    <option value="deu">German (Deutsch)</option>
                    <option value="spa">Spanish (Español)</option>
                    <option value="ita">Italian (Italiano)</option>
                    <option value="por">Portuguese (Português)</option>
                    <option value="rus">Russian (Русский)</option>
                    <option value="chi_sim">Chinese Simplified (简体中文)</option>
                    <option value="chi_tra">Chinese Traditional (繁體中文)</option>
                    <option value="jpn">Japanese (日本語)</option>
                    <option value="kor">Korean (한국어)</option>
                </select>
            </div>
            
            <!-- Output Format -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Output Format</label>
                <select id="outputFormat" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="text">Plain Text</option>
                    <option value="searchable_pdf">Searchable PDF</option>
                    <option value="json">JSON with Coordinates</option>
                    <option value="html">HTML Format</option>
                </select>
            </div>
        </div>
        
        <!-- Advanced Options -->
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Advanced Options</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="enhanceImage" checked class="mr-2">
                    Enhance image quality for better OCR
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="preserveLayout" class="mr-2">
                    Preserve document layout
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="detectTables" class="mr-2">
                    Detect and extract tables
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="confidenceScore" checked class="mr-2">
                    Include confidence scores
                </label>
            </div>
        </div>
        
        <!-- Page Range -->
        <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Page Range (Optional)</label>
            <input type="text" id="pageRange" placeholder="e.g., 1-5 or 1,3,5 (leave empty for all pages)" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview OCR
            </button>
            <button id="processBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-eye mr-2"></i>Process OCR
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Processing OCR on your PDF...</p>
            <div class="mt-4">
                <div class="bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p id="progressText" class="text-sm text-gray-600 mt-2">Initializing...</p>
            </div>
        </div>
    </div>
    
    <!-- Results -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center mb-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600">OCR processing completed successfully!</p>
        </div>
        
        <!-- OCR Statistics -->
        <div id="ocrStats" class="bg-gray-50 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">OCR Results Summary</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600" id="totalPages">0</div>
                    <div class="text-sm text-gray-600">Pages Processed</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600" id="totalWords">0</div>
                    <div class="text-sm text-gray-600">Words Extracted</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600" id="avgConfidence">0%</div>
                    <div class="text-sm text-gray-600">Avg. Confidence</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-orange-600" id="processingTime">0s</div>
                    <div class="text-sm text-gray-600">Processing Time</div>
                </div>
            </div>
        </div>
        
        <!-- Extracted Text Preview -->
        <div id="textPreview" class="mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Extracted Text Preview</h4>
            <div class="bg-gray-50 border rounded-lg p-4 max-h-64 overflow-y-auto">
                <pre id="extractedText" class="text-sm text-gray-700 whitespace-pre-wrap"></pre>
            </div>
        </div>
        
        <!-- Download Options -->
        <div class="flex justify-center space-x-4">
            <button id="downloadTextBtn" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-download mr-2"></i>Download Text
            </button>
            <button id="downloadPdfBtn" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors hidden">
                <i class="fas fa-file-pdf mr-2"></i>Download Searchable PDF
            </button>
            <button id="downloadJsonBtn" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors hidden">
                <i class="fas fa-code mr-2"></i>Download JSON
            </button>
            <button id="newOcrBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Process Another PDF
            </button>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-blue-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use OCR:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-blue-700">
            <li>Upload a scanned PDF or image-based PDF</li>
            <li>Select the language of the text in your document</li>
            <li>Choose output format (text, searchable PDF, JSON, or HTML)</li>
            <li>Configure advanced options for better results</li>
            <li>Process the OCR and download your results</li>
        </ol>
        
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-yellow-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tips for better OCR results:</strong>
                <br>• Use high-resolution scanned documents
                <br>• Ensure text is clear and not skewed
                <br>• Select the correct language for your document
                <br>• Enable image enhancement for low-quality scans
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedFile = null;
let ocrResult = null;
let startTime = null;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // File upload
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type === 'application/pdf') {
            fileInput.files = files;
            handleFileSelect();
        }
    });
    
    // Buttons
    document.getElementById('previewBtn').addEventListener('click', previewOCR);
    document.getElementById('processBtn').addEventListener('click', processOCR);
    document.getElementById('downloadTextBtn').addEventListener('click', downloadText);
    document.getElementById('downloadPdfBtn').addEventListener('click', downloadPDF);
    document.getElementById('downloadJsonBtn').addEventListener('click', downloadJSON);
    document.getElementById('newOcrBtn').addEventListener('click', resetForm);
    
    // Output format change
    document.getElementById('outputFormat').addEventListener('change', updateDownloadButtons);
}

function handleFileSelect() {
    const file = document.getElementById('fileInput').files[0];
    if (file && file.type === 'application/pdf') {
        selectedFile = file;
        displayFileInfo(file);
        document.getElementById('ocrSettings').classList.remove('hidden');
    }
}

function displayFileInfo(file) {
    const fileInfo = document.getElementById('fileInfo');
    const fileDetails = document.getElementById('fileDetails');
    
    fileDetails.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
            <div>
                <p class="font-medium text-gray-800">${file.name}</p>
                <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
            </div>
        </div>
    `;
    
    fileInfo.classList.remove('hidden');
}

function previewOCR() {
    if (!selectedFile) {
        alert('Please select a PDF file first.');
        return;
    }
    
    const language = document.getElementById('ocrLanguage').value;
    const outputFormat = document.getElementById('outputFormat').value;
    const pageRange = document.getElementById('pageRange').value;
    
    alert(`OCR Preview Settings:
    
Language: ${document.getElementById('ocrLanguage').selectedOptions[0].text}
Output Format: ${outputFormat}
Page Range: ${pageRange || 'All pages'}
Image Enhancement: ${document.getElementById('enhanceImage').checked ? 'Enabled' : 'Disabled'}

Click "Process OCR" to start text extraction.`);
}

function processOCR() {
    if (!selectedFile) {
        alert('Please select a PDF file first.');
        return;
    }
    
    startTime = Date.now();
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('language', document.getElementById('ocrLanguage').value);
    formData.append('output_format', document.getElementById('outputFormat').value);
    formData.append('enhance_image', document.getElementById('enhanceImage').checked);
    formData.append('preserve_layout', document.getElementById('preserveLayout').checked);
    formData.append('detect_tables', document.getElementById('detectTables').checked);
    formData.append('confidence_score', document.getElementById('confidenceScore').checked);
    formData.append('page_range', document.getElementById('pageRange').value);
    
    // Show progress
    document.getElementById('ocrSettings').classList.add('hidden');
    document.getElementById('progressSection').classList.remove('hidden');
    
    // Simulate progress
    simulateProgress();
    
    fetch('/api/pdf_to_ocr', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error('OCR processing failed');
        }
    })
    .then(data => {
        ocrResult = data;
        document.getElementById('progressSection').classList.add('hidden');
        displayResults(data);
    })
    .catch(error => {
        alert('Error processing OCR: ' + error.message);
        document.getElementById('progressSection').classList.add('hidden');
        document.getElementById('ocrSettings').classList.remove('hidden');
    });
}

function simulateProgress() {
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const steps = [
        'Analyzing PDF structure...',
        'Converting pages to images...',
        'Enhancing image quality...',
        'Running OCR recognition...',
        'Processing text layout...',
        'Finalizing results...'
    ];
    
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 95) progress = 95;
        
        progressBar.style.width = progress + '%';
        const stepIndex = Math.floor((progress / 100) * steps.length);
        if (stepIndex < steps.length) {
            progressText.textContent = steps[stepIndex];
        }
    }, 500);
    
    // Clear interval after 10 seconds max
    setTimeout(() => {
        clearInterval(interval);
        progressBar.style.width = '100%';
        progressText.textContent = 'Completing...';
    }, 10000);
}

function displayResults(data) {
    document.getElementById('resultSection').classList.remove('hidden');
    
    // Update statistics
    const processingTime = Math.round((Date.now() - startTime) / 1000);
    document.getElementById('totalPages').textContent = data.total_pages || 0;
    document.getElementById('totalWords').textContent = data.full_text ? data.full_text.split(' ').length : 0;
    document.getElementById('avgConfidence').textContent = (data.overall_confidence || 0) + '%';
    document.getElementById('processingTime').textContent = processingTime + 's';
    
    // Display extracted text preview
    const extractedText = document.getElementById('extractedText');
    const previewText = data.full_text ? data.full_text.substring(0, 1000) : 'No text extracted';
    extractedText.textContent = previewText + (data.full_text && data.full_text.length > 1000 ? '...' : '');
    
    // Update download buttons
    updateDownloadButtons();
}

function updateDownloadButtons() {
    const outputFormat = document.getElementById('outputFormat').value;
    
    document.getElementById('downloadTextBtn').style.display = 'inline-block';
    document.getElementById('downloadPdfBtn').style.display = outputFormat === 'searchable_pdf' ? 'inline-block' : 'none';
    document.getElementById('downloadJsonBtn').style.display = outputFormat === 'json' ? 'inline-block' : 'none';
}

function downloadText() {
    if (!ocrResult || !ocrResult.full_text) {
        alert('No text available for download.');
        return;
    }
    
    const blob = new Blob([ocrResult.full_text], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted_text.txt';
    a.click();
    window.URL.revokeObjectURL(url);
}

function downloadPDF() {
    if (!selectedFile) return;
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('language', document.getElementById('ocrLanguage').value);
    
    fetch('/api/create_searchable_pdf', {
        method: 'POST',
        body: formData
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'searchable.pdf';
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        alert('Error creating searchable PDF: ' + error.message);
    });
}

function downloadJSON() {
    if (!ocrResult) {
        alert('No OCR data available for download.');
        return;
    }
    
    const blob = new Blob([JSON.stringify(ocrResult, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ocr_results.json';
    a.click();
    window.URL.revokeObjectURL(url);
}

function resetForm() {
    selectedFile = null;
    ocrResult = null;
    
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').classList.add('hidden');
    document.getElementById('ocrSettings').classList.add('hidden');
    document.getElementById('resultSection').classList.add('hidden');
    document.getElementById('progressSection').classList.add('hidden');
    
    // Reset form values
    document.getElementById('ocrLanguage').value = 'eng';
    document.getElementById('outputFormat').value = 'text';
    document.getElementById('pageRange').value = '';
    document.getElementById('enhanceImage').checked = true;
    document.getElementById('preserveLayout').checked = false;
    document.getElementById('detectTables').checked = false;
    document.getElementById('confidenceScore').checked = true;
}
</script>
{% endblock %}
