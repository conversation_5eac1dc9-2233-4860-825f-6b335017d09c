<!DOCTYPE html>
<html lang="{{ lang }}" dir="{{ 'rtl' if lang == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ t.site_name }} - {{ t.tagline }}{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    
    <style>
        body {
            font-family: {{ "'Cairo', sans-serif" if lang == 'ar' else "'Inter', sans-serif" }};
        }
        
        .tool-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }
        
        .glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
        }
        
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        
        .drag-area.dragover {
            border-color: #667eea;
            background-color: #f7fafc;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <i class="fas fa-file-pdf text-3xl text-red-600"></i>
                        <span class="text-2xl font-bold text-gray-800">{{ t.site_name }}</span>
                    </a>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-gray-700 hover:text-blue-600 transition-colors">{{ t.home }}</a>
                    
                    <!-- Tools Dropdown -->
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 transition-colors flex items-center">
                            {{ t.tools }}
                            <i class="fas fa-chevron-down ml-1 text-sm"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                            <div class="grid grid-cols-2 gap-2 p-4">
                                <a href="/tool/merge_pdf" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded">{{ t.tools_list.merge_pdf.name }}</a>
                                <a href="/tool/split_pdf" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded">{{ t.tools_list.split_pdf.name }}</a>
                                <a href="/tool/compress_pdf" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded">{{ t.tools_list.compress_pdf.name }}</a>
                                <a href="/tool/protect_pdf" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded">{{ t.tools_list.protect_pdf.name }}</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Help Dropdown -->
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 transition-colors flex items-center">
                            {{ t.help }}
                            <i class="fas fa-chevron-down ml-1 text-sm"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                            <div class="py-2">
                                <a href="/features" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50">{{ t.features }}</a>
                                <a href="/about" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50">{{ t.about }}</a>
                                <a href="/faq" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50">{{ t.faq }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Language Switcher -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <select id="languageSelect" class="bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="en" {{ 'selected' if lang == 'en' else '' }}>{{ t.english }}</option>
                            <option value="ar" {{ 'selected' if lang == 'ar' else '' }}>{{ t.arabic }}</option>
                        </select>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button id="mobileMenuBtn" class="md:hidden text-gray-700 hover:text-blue-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobileMenu" class="md:hidden bg-white border-t border-gray-200 hidden">
            <div class="px-4 py-2 space-y-2">
                <a href="/" class="block py-2 text-gray-700 hover:text-blue-600">{{ t.home }}</a>
                <a href="#" class="block py-2 text-gray-700 hover:text-blue-600">{{ t.tools }}</a>
                <a href="#" class="block py-2 text-gray-700 hover:text-blue-600">{{ t.help }}</a>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12 mt-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <i class="fas fa-file-pdf text-2xl text-red-500"></i>
                        <span class="text-xl font-bold">{{ t.site_name }}</span>
                    </div>
                    <p class="text-gray-400 mb-4">{{ t.tagline }}</p>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ t.help }}</h3>
                    <ul class="space-y-2">
                        <li><a href="/features" class="text-gray-400 hover:text-white transition-colors">{{ t.features }}</a></li>
                        <li><a href="/about" class="text-gray-400 hover:text-white transition-colors">{{ t.about }}</a></li>
                        <li><a href="/faq" class="text-gray-400 hover:text-white transition-colors">{{ t.faq }}</a></li>
                    </ul>
                </div>
                
                <!-- Legal -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="/terms" class="text-gray-400 hover:text-white transition-colors">{{ t.terms }}</a></li>
                        <li><a href="/privacy" class="text-gray-400 hover:text-white transition-colors">{{ t.privacy }}</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">{{ t.copyright }}</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // Language switcher
        document.getElementById('languageSelect').addEventListener('change', function() {
            const lang = this.value;
            fetch(`/set_language/${lang}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        location.reload();
                    }
                });
        });
        
        // Mobile menu toggle
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        });
    </script>
    

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 max-w-sm w-full mx-4">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-700 font-medium">{{ t.upload.processing }}</p>
            </div>
        </div>
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
