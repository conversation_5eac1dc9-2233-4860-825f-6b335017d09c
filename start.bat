@echo off
title PDF2Any - Professional PDF Processing Web Application

echo ================================================
echo 🚀 Starting PDF2Any Application
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "app.py" (
    echo ❌ app.py not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

REM Install requirements if needed
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
)

echo 📦 Activating virtual environment...
call venv\Scripts\activate.bat

echo 📦 Installing/updating requirements...
pip install -r requirements.txt

echo.
echo ================================================
echo 🌟 PDF2Any Features:
echo ================================================
echo • 25 Professional PDF Tools
echo • Bilingual Support (English/Arabic)
echo • Modern Responsive UI
echo • Advanced Watermarking
echo • PDF Translation with Google Translate
echo • Image Processing ^& Conversion
echo • Text to PDF Conversion
echo • OCR Text Extraction
echo • And much more!
echo.

echo ================================================
echo 🔧 Starting Development Server...
echo ================================================
echo Server will be available at: http://localhost:5000
echo Press Ctrl+C to stop the server
echo ================================================
echo.

REM Start the application
python run.py

echo.
echo ✅ Server stopped successfully
pause
