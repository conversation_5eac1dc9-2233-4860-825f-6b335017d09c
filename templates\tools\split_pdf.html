{% extends "base.html" %}

{% block title %}Split PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-cut text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Split PDF</h1>
        <p class="text-xl text-gray-600">Split PDF into individual pages or page ranges</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to split</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Split Options -->
    <div id="splitOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Split Options</h2>
        
        <div class="space-y-4">
            <label class="flex items-center">
                <input type="radio" name="splitType" value="all" checked class="mr-2">
                <span class="font-medium">Split into individual pages</span>
                <span class="ml-2 text-sm text-gray-500">(Each page becomes a separate PDF)</span>
            </label>
            
            <label class="flex items-center">
                <input type="radio" name="splitType" value="range" class="mr-2">
                <span class="font-medium">Split by page ranges</span>
                <span class="ml-2 text-sm text-gray-500">(Specify custom ranges)</span>
            </label>
            
            <label class="flex items-center">
                <input type="radio" name="splitType" value="interval" class="mr-2">
                <span class="font-medium">Split by interval</span>
                <span class="ml-2 text-sm text-gray-500">(Every N pages)</span>
            </label>
        </div>
        
        <!-- Range Options -->
        <div id="rangeOptions" class="mt-6 hidden">
            <label class="block text-sm font-medium text-gray-700 mb-2">Page Ranges</label>
            <input type="text" id="pageRanges" placeholder="e.g., 1-5, 6-10, 11-15" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
            <p class="text-sm text-gray-500 mt-1">Enter ranges separated by commas</p>
        </div>
        
        <!-- Interval Options -->
        <div id="intervalOptions" class="mt-6 hidden">
            <label class="block text-sm font-medium text-gray-700 mb-2">Pages per file</label>
            <input type="number" id="pagesPerFile" value="5" min="1" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Split
            </button>
            <button id="splitBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-cut mr-2"></i>Split PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Splitting your PDF file...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully split!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>Download ZIP
                </button>
                <button id="newSplitBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Split Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-orange-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-orange-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-orange-700">
            <li>Upload your PDF file</li>
            <li>Choose split method: individual pages, ranges, or intervals</li>
            <li>Configure split settings based on your choice</li>
            <li>Preview the split before processing</li>
            <li>Split your PDF and download the ZIP file containing all parts</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Split type change
        document.querySelectorAll('input[name="splitType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const rangeOptions = document.getElementById('rangeOptions');
                const intervalOptions = document.getElementById('intervalOptions');
                
                rangeOptions.classList.add('hidden');
                intervalOptions.classList.add('hidden');
                
                if (this.value === 'range') {
                    rangeOptions.classList.remove('hidden');
                } else if (this.value === 'interval') {
                    intervalOptions.classList.remove('hidden');
                }
            });
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewSplit);
        document.getElementById('splitBtn').addEventListener('click', splitPDF);
        document.getElementById('newSplitBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('splitOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function previewSplit() {
        const splitType = document.querySelector('input[name="splitType"]:checked').value;
        let message = 'Split Preview:\n\n';
        
        switch (splitType) {
            case 'all':
                message += 'Each page will become a separate PDF file.';
                break;
            case 'range':
                const ranges = document.getElementById('pageRanges').value;
                if (!ranges.trim()) {
                    alert('Please specify page ranges.');
                    return;
                }
                message += `PDF will be split into the following ranges: ${ranges}`;
                break;
            case 'interval':
                const interval = document.getElementById('pagesPerFile').value;
                message += `PDF will be split every ${interval} pages.`;
                break;
        }
        
        alert(message);
    }
    
    function splitPDF() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const splitType = document.querySelector('input[name="splitType"]:checked').value;
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('split_type', splitType);
        
        if (splitType === 'range') {
            const ranges = document.getElementById('pageRanges').value;
            if (!ranges.trim()) {
                alert('Please specify page ranges.');
                return;
            }
            formData.append('page_ranges', ranges);
        } else if (splitType === 'interval') {
            formData.append('pages_per_file', document.getElementById('pagesPerFile').value);
        }
        
        // Show progress
        document.getElementById('splitOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/split_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Split failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'split_pdf.zip';
                a.click();
            };
        })
        .catch(error => {
            alert('Error splitting PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('splitOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('splitOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset form values
        document.querySelector('input[name="splitType"][value="all"]').checked = true;
        document.getElementById('rangeOptions').classList.add('hidden');
        document.getElementById('intervalOptions').classList.add('hidden');
        document.getElementById('pageRanges').value = '';
        document.getElementById('pagesPerFile').value = '5';
    }
</script>
{% endblock %}
