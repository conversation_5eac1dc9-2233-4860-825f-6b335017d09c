import os
import tempfile
import uuid
import fitz  # PyMuPDF
from PIL import Image
import pytesseract
import cv2
import numpy as np
from pdf2image import convert_from_bytes
import json

class OCRProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        
        # Configure tesseract path if needed (Windows)
        if os.name == 'nt':
            # Try common Windows installation paths
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', ''))
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    break
    
    def extract_text_with_ocr(self, file, language='eng', enhance_image=True,
                             preserve_layout=False, detect_tables=False,
                             confidence_score=True, page_range=''):
        """Extract text from PDF using OCR with advanced options"""
        try:
            if not file:
                raise ValueError("No file provided")

            # Reset file pointer
            file.seek(0)
            pdf_bytes = file.read()

            if not pdf_bytes:
                raise ValueError("Empty file provided")

            # Convert PDF pages to images
            images = convert_from_bytes(pdf_bytes, dpi=300)

            if not images:
                raise ValueError("No pages found in PDF")

            # Parse page range if provided
            pages_to_process = self._parse_page_range(page_range, len(images))

            extracted_data = {
                'pages': [],
                'full_text': '',
                'confidence_scores': [],
                'total_pages': len(images),
                'processed_pages': len(pages_to_process),
                'overall_confidence': 0,
                'processing_time': 0,
                'language': language,
                'settings': {
                    'enhance_image': enhance_image,
                    'preserve_layout': preserve_layout,
                    'detect_tables': detect_tables,
                    'confidence_score': confidence_score
                }
            }

            import time
            start_time = time.time()

            for i, page_num in enumerate(pages_to_process):
                # Convert PIL image to numpy array for OpenCV processing
                img_array = np.array(image)
                
                if enhance_image:
                    img_array = self._enhance_image_for_ocr(img_array)
                
                # Convert back to PIL Image
                enhanced_image = Image.fromarray(img_array)
                
                # Perform OCR with detailed data
                ocr_data = pytesseract.image_to_data(
                    enhanced_image, 
                    lang=language, 
                    output_type=pytesseract.Output.DICT
                )
                
                # Extract text
                page_text = pytesseract.image_to_string(enhanced_image, lang=language)
                
                # Calculate confidence score
                confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                
                page_data = {
                    'page_number': page_num + 1,
                    'text': page_text.strip(),
                    'confidence': round(avg_confidence, 2),
                    'word_count': len(page_text.split()),
                    'bounding_boxes': self._extract_bounding_boxes(ocr_data)
                }
                
                extracted_data['pages'].append(page_data)
                extracted_data['full_text'] += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
                extracted_data['confidence_scores'].append(avg_confidence)
            
            # Calculate overall confidence
            if extracted_data['confidence_scores']:
                extracted_data['overall_confidence'] = round(
                    sum(extracted_data['confidence_scores']) / len(extracted_data['confidence_scores']), 2
                )
            else:
                extracted_data['overall_confidence'] = 0
            
            return extracted_data
            
        except Exception as e:
            raise Exception(f"OCR processing failed: {str(e)}")

    def _parse_page_range(self, page_range, total_pages):
        """Parse page range string and return list of page indices"""
        if not page_range or page_range.strip() == '':
            return list(range(total_pages))

        pages = []
        try:
            # Handle different formats: "1-5", "1,3,5", "1-3,7,9-12"
            parts = page_range.replace(' ', '').split(',')

            for part in parts:
                if '-' in part:
                    # Range format: "1-5"
                    start, end = part.split('-')
                    start = max(1, int(start))
                    end = min(total_pages, int(end))
                    pages.extend(range(start - 1, end))  # Convert to 0-based
                else:
                    # Single page: "3"
                    page = int(part)
                    if 1 <= page <= total_pages:
                        pages.append(page - 1)  # Convert to 0-based

            # Remove duplicates and sort
            pages = sorted(list(set(pages)))

        except ValueError:
            # If parsing fails, process all pages
            pages = list(range(total_pages))

        return pages if pages else list(range(total_pages))
    
    def _enhance_image_for_ocr(self, img_array):
        """Enhance image quality for better OCR results"""
        # Convert to grayscale
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Apply morphological operations to clean up
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def _extract_bounding_boxes(self, ocr_data):
        """Extract bounding box information from OCR data"""
        boxes = []
        n_boxes = len(ocr_data['text'])
        
        for i in range(n_boxes):
            if int(ocr_data['conf'][i]) > 30:  # Only include confident detections
                box = {
                    'text': ocr_data['text'][i],
                    'confidence': int(ocr_data['conf'][i]),
                    'left': int(ocr_data['left'][i]),
                    'top': int(ocr_data['top'][i]),
                    'width': int(ocr_data['width'][i]),
                    'height': int(ocr_data['height'][i])
                }
                boxes.append(box)
        
        return boxes
    
    def create_searchable_pdf(self, file, language='eng'):
        """Create a searchable PDF by adding OCR text layer"""
        try:
            pdf_bytes = file.read()
            pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            
            # Convert pages to images for OCR
            images = convert_from_bytes(pdf_bytes, dpi=300)
            
            output_path = os.path.join(self.temp_dir, f'searchable_pdf_{uuid.uuid4().hex}.pdf')
            
            # Create new PDF document
            new_doc = fitz.open()
            
            for page_num, image in enumerate(images):
                # Get original page
                original_page = pdf_doc[page_num]
                
                # Convert PIL image to numpy array
                img_array = np.array(image)
                enhanced_img = self._enhance_image_for_ocr(img_array)
                enhanced_image = Image.fromarray(enhanced_img)
                
                # Get OCR data with bounding boxes
                ocr_data = pytesseract.image_to_data(
                    enhanced_image, 
                    lang=language, 
                    output_type=pytesseract.Output.DICT
                )
                
                # Create new page with same dimensions
                page_rect = original_page.rect
                new_page = new_doc.new_page(width=page_rect.width, height=page_rect.height)
                
                # Insert original page as image
                original_pix = original_page.get_pixmap()
                new_page.insert_image(page_rect, pixmap=original_pix)
                
                # Add invisible text layer
                self._add_invisible_text_layer(new_page, ocr_data, page_rect)
            
            # Save the new PDF
            new_doc.save(output_path)
            new_doc.close()
            pdf_doc.close()
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Searchable PDF creation failed: {str(e)}")
    
    def _add_invisible_text_layer(self, page, ocr_data, page_rect):
        """Add invisible text layer to PDF page"""
        n_boxes = len(ocr_data['text'])
        
        for i in range(n_boxes):
            if int(ocr_data['conf'][i]) > 30 and ocr_data['text'][i].strip():
                # Calculate position and size
                left = int(ocr_data['left'][i])
                top = int(ocr_data['top'][i])
                width = int(ocr_data['width'][i])
                height = int(ocr_data['height'][i])
                
                # Convert image coordinates to PDF coordinates
                # PDF coordinates start from bottom-left, image from top-left
                pdf_x = left * page_rect.width / 2480  # Assuming 300 DPI conversion
                pdf_y = page_rect.height - (top + height) * page_rect.height / 3508
                pdf_width = width * page_rect.width / 2480
                pdf_height = height * page_rect.height / 3508
                
                # Create text rectangle
                text_rect = fitz.Rect(pdf_x, pdf_y, pdf_x + pdf_width, pdf_y + pdf_height)
                
                # Insert invisible text
                page.insert_text(
                    text_rect.tl,
                    ocr_data['text'][i],
                    fontsize=pdf_height,
                    color=(1, 1, 1),  # White text (invisible)
                    render_mode=3  # Invisible text mode
                )
    
    def get_supported_languages(self):
        """Get list of supported OCR languages"""
        try:
            languages = pytesseract.get_languages()
            
            # Common language mappings
            language_names = {
                'eng': 'English',
                'ara': 'Arabic',
                'fra': 'French',
                'deu': 'German',
                'spa': 'Spanish',
                'ita': 'Italian',
                'por': 'Portuguese',
                'rus': 'Russian',
                'chi_sim': 'Chinese (Simplified)',
                'chi_tra': 'Chinese (Traditional)',
                'jpn': 'Japanese',
                'kor': 'Korean'
            }
            
            supported = []
            for lang_code in languages:
                lang_name = language_names.get(lang_code, lang_code.title())
                supported.append({
                    'code': lang_code,
                    'name': lang_name
                })
            
            return supported
            
        except Exception as e:
            # Return default languages if detection fails
            return [
                {'code': 'eng', 'name': 'English'},
                {'code': 'ara', 'name': 'Arabic'},
                {'code': 'fra', 'name': 'French'},
                {'code': 'deu', 'name': 'German'},
                {'code': 'spa', 'name': 'Spanish'}
            ]
    
    def analyze_document_layout(self, file):
        """Analyze document layout and structure"""
        try:
            pdf_bytes = file.read()
            images = convert_from_bytes(pdf_bytes, dpi=200)
            
            layout_analysis = {
                'pages': [],
                'document_type': 'unknown',
                'has_tables': False,
                'has_images': False,
                'text_regions': []
            }
            
            for page_num, image in enumerate(images):
                img_array = np.array(image)
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                
                # Detect text regions
                text_regions = self._detect_text_regions(gray)
                
                # Detect tables
                has_table = self._detect_tables(gray)
                
                # Detect images
                has_image = self._detect_images(gray)
                
                page_analysis = {
                    'page_number': page_num + 1,
                    'text_regions': len(text_regions),
                    'has_table': has_table,
                    'has_image': has_image,
                    'layout_type': self._classify_layout(text_regions, has_table, has_image)
                }
                
                layout_analysis['pages'].append(page_analysis)
                
                if has_table:
                    layout_analysis['has_tables'] = True
                if has_image:
                    layout_analysis['has_images'] = True
            
            return layout_analysis
            
        except Exception as e:
            raise Exception(f"Layout analysis failed: {str(e)}")
    
    def _detect_text_regions(self, gray_image):
        """Detect text regions in image"""
        # Use morphological operations to detect text regions
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (18, 18))
        connected = cv2.morphologyEx(gray_image, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        text_regions = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if w > 50 and h > 20:  # Filter small regions
                text_regions.append((x, y, w, h))
        
        return text_regions
    
    def _detect_tables(self, gray_image):
        """Detect if image contains tables"""
        # Use Hough line detection to find table lines
        edges = cv2.Canny(gray_image, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is not None and len(lines) > 10:
            return True
        return False
    
    def _detect_images(self, gray_image):
        """Detect if page contains images"""
        # Simple image detection based on large uniform regions
        blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)
        contours, _ = cv2.findContours(blurred, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 10000:  # Large regions might be images
                return True
        return False
    
    def _classify_layout(self, text_regions, has_table, has_image):
        """Classify page layout type"""
        if has_table and has_image:
            return 'mixed'
        elif has_table:
            return 'table'
        elif has_image:
            return 'image_heavy'
        elif len(text_regions) > 3:
            return 'multi_column'
        else:
            return 'simple_text'
