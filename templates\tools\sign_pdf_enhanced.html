{% extends "base.html" %}

{% block title %}Sign PDF - {{ t.site_name }}{% endblock %}

{% block extra_head %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script src="{{ url_for('static', filename='js/pdf-viewer.js') }}"></script>
<style>
.signature-preview {
    position: absolute;
    pointer-events: none;
    border: 2px dashed #10b981;
    background: rgba(16, 185, 129, 0.1);
    z-index: 10;
    border-radius: 4px;
    min-width: 100px;
    min-height: 40px;
}

.signature-canvas {
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    cursor: crosshair;
    background: white;
}

.pdf-viewer {
    height: 600px;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
}

.signature-pad {
    touch-action: none;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-signature text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Sign PDF</h1>
        <p class="text-xl text-gray-600">Add digital signatures to your PDF documents</p>
    </div>
    
    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Panel - Controls -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Upload Area -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Upload PDF</h2>
                <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-all duration-300">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-4">Drag & drop PDF or click to select</p>
                    <input type="file" id="fileInput" accept=".pdf" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-4 py-2 rounded-lg">
                        Select PDF File
                    </button>
                </div>
                
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                            <div>
                                <p id="fileName" class="font-medium text-gray-800"></p>
                                <p id="fileSize" class="text-sm text-gray-500"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Signature Type -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Signature Type</h2>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="radio" name="signatureType" value="draw" checked class="mr-2">
                        <i class="fas fa-pen mr-2 text-blue-500"></i>
                        Draw Signature
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="signatureType" value="text" class="mr-2">
                        <i class="fas fa-font mr-2 text-green-500"></i>
                        Text Signature
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="signatureType" value="image" class="mr-2">
                        <i class="fas fa-image mr-2 text-purple-500"></i>
                        Image Signature
                    </label>
                </div>
            </div>
            
            <!-- Draw Signature -->
            <div id="drawSignature" class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Draw Your Signature</h2>
                <div class="space-y-4">
                    <canvas id="signatureCanvas" class="signature-canvas w-full" width="300" height="120"></canvas>
                    
                    <div class="flex justify-between">
                        <button id="clearSignature" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-eraser mr-2"></i>Clear
                        </button>
                        <button id="saveSignature" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-save mr-2"></i>Save
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Pen Color</label>
                            <input type="color" id="penColor" value="#000000" class="w-full h-8 border border-gray-300 rounded">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Pen Width</label>
                            <input type="range" id="penWidth" min="1" max="10" value="2" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="penWidthValue">2</span>px</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Text Signature -->
            <div id="textSignature" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Text Signature</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Signature Text</label>
                        <input type="text" id="signatureText" placeholder="Enter your name" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Font</label>
                            <select id="signatureFont" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                <option value="cursive">Cursive</option>
                                <option value="serif">Serif</option>
                                <option value="sans-serif">Sans-serif</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                            <input type="range" id="textSize" min="16" max="48" value="24" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="textSizeValue">24</span>px</div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                        <input type="color" id="textColor" value="#000000" class="w-full h-8 border border-gray-300 rounded">
                    </div>
                    
                    <div id="textPreview" class="p-4 border border-gray-300 rounded-lg bg-gray-50 text-center">
                        <span style="font-family: cursive; font-size: 24px; color: #000000;">Your Name</span>
                    </div>
                </div>
            </div>
            
            <!-- Image Signature -->
            <div id="imageSignature" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Image Signature</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Signature Image</label>
                        <input type="file" id="signatureImageInput" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div id="imagePreview" class="hidden">
                        <img id="previewImg" class="max-w-full h-32 mx-auto border border-gray-300 rounded">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Width</label>
                            <input type="range" id="imageWidth" min="50" max="300" value="150" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="imageWidthValue">150</span>px</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Height</label>
                            <input type="range" id="imageHeight" min="30" max="200" value="75" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="imageHeightValue">75</span>px</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Info -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Additional Information</h2>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="addDate" checked class="mr-2">
                        Add current date
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="addTime" class="mr-2">
                        Add current time
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="addLocation" class="mr-2">
                        Add location
                    </label>
                    
                    <div id="locationInput" class="hidden">
                        <input type="text" id="signatureLocation" placeholder="Enter location" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="space-y-3">
                    <button id="previewSignature" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-eye mr-2"></i>Preview Signature
                    </button>
                    <button id="applySignature" class="w-full btn-primary text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-signature mr-2"></i>Apply Signature
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Right Panel - PDF Viewer -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">PDF Preview</h2>
                <div class="text-sm text-blue-600 mb-4">
                    <i class="fas fa-info-circle mr-1"></i>
                    Click on the PDF where you want to place your signature
                </div>
                <div id="pdfViewerContainer" class="relative">
                    <div id="pdfViewer" class="pdf-viewer">
                        <div class="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-file-pdf text-6xl mb-4"></i>
                                <p>Upload a PDF file to start signing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Progress Modal -->
    <div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full animate-spin mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Processing</h3>
                <p class="text-gray-600">Adding signature to your PDF...</p>
            </div>
        </div>
    </div>
    
    <!-- Result Modal -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Signature Applied!</h3>
                <p class="text-gray-600 mb-6">Your PDF has been successfully signed.</p>
                
                <div class="flex justify-center space-x-4">
                    <button id="downloadBtn" class="btn-primary text-white px-6 py-2 rounded-lg font-semibold">
                        <i class="fas fa-download mr-2"></i>Download
                    </button>
                    <button id="newSignatureBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        Sign Another PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class SignatureTool {
    constructor() {
        this.pdfViewer = null;
        this.selectedFile = null;
        this.signaturePreviews = [];
        this.currentSignatureType = 'draw';
        this.signatureCanvas = null;
        this.isDrawing = false;
        this.savedSignature = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeSignatureCanvas();
        this.updateSliderValues();
    }

    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                this.handleFileSelect(files[0]);
            }
        });

        // Signature type selection
        document.querySelectorAll('input[name="signatureType"]').forEach(radio => {
            radio.addEventListener('change', (e) => this.switchSignatureType(e.target.value));
        });

        // Draw signature controls
        document.getElementById('clearSignature').addEventListener('click', () => this.clearCanvas());
        document.getElementById('saveSignature').addEventListener('click', () => this.saveDrawnSignature());
        document.getElementById('penWidth').addEventListener('input', (e) => {
            document.getElementById('penWidthValue').textContent = e.target.value;
        });

        // Text signature controls
        document.getElementById('signatureText').addEventListener('input', () => this.updateTextPreview());
        document.getElementById('signatureFont').addEventListener('change', () => this.updateTextPreview());
        document.getElementById('textSize').addEventListener('input', (e) => {
            document.getElementById('textSizeValue').textContent = e.target.value;
            this.updateTextPreview();
        });
        document.getElementById('textColor').addEventListener('change', () => this.updateTextPreview());

        // Image signature controls
        document.getElementById('signatureImageInput').addEventListener('change', (e) => this.handleImageSelect(e.target.files[0]));
        document.getElementById('imageWidth').addEventListener('input', (e) => {
            document.getElementById('imageWidthValue').textContent = e.target.value;
        });
        document.getElementById('imageHeight').addEventListener('input', (e) => {
            document.getElementById('imageHeightValue').textContent = e.target.value;
        });

        // Additional info
        document.getElementById('addLocation').addEventListener('change', (e) => {
            document.getElementById('locationInput').classList.toggle('hidden', !e.target.checked);
        });

        // Action buttons
        document.getElementById('previewSignature').addEventListener('click', () => this.previewSignature());
        document.getElementById('applySignature').addEventListener('click', () => this.applySignature());
        document.getElementById('newSignatureBtn').addEventListener('click', () => this.resetTool());
    }

    initializeSignatureCanvas() {
        this.signatureCanvas = document.getElementById('signatureCanvas');
        const ctx = this.signatureCanvas.getContext('2d');

        // Set canvas background
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, this.signatureCanvas.width, this.signatureCanvas.height);

        // Mouse events
        this.signatureCanvas.addEventListener('mousedown', (e) => this.startDrawing(e));
        this.signatureCanvas.addEventListener('mousemove', (e) => this.draw(e));
        this.signatureCanvas.addEventListener('mouseup', () => this.stopDrawing());
        this.signatureCanvas.addEventListener('mouseout', () => this.stopDrawing());

        // Touch events for mobile
        this.signatureCanvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            this.signatureCanvas.dispatchEvent(mouseEvent);
        });

        this.signatureCanvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousemove', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            this.signatureCanvas.dispatchEvent(mouseEvent);
        });

        this.signatureCanvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            const mouseEvent = new MouseEvent('mouseup', {});
            this.signatureCanvas.dispatchEvent(mouseEvent);
        });
    }

    startDrawing(e) {
        this.isDrawing = true;
        const rect = this.signatureCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const ctx = this.signatureCanvas.getContext('2d');
        ctx.beginPath();
        ctx.moveTo(x, y);
    }

    draw(e) {
        if (!this.isDrawing) return;

        const rect = this.signatureCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const ctx = this.signatureCanvas.getContext('2d');
        ctx.lineWidth = document.getElementById('penWidth').value;
        ctx.lineCap = 'round';
        ctx.strokeStyle = document.getElementById('penColor').value;

        ctx.lineTo(x, y);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x, y);
    }

    stopDrawing() {
        this.isDrawing = false;
    }

    clearCanvas() {
        const ctx = this.signatureCanvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, this.signatureCanvas.width, this.signatureCanvas.height);
    }

    saveDrawnSignature() {
        this.savedSignature = this.signatureCanvas.toDataURL();
        alert('Signature saved! You can now place it on the PDF.');
    }

    updateSliderValues() {
        document.getElementById('penWidthValue').textContent = document.getElementById('penWidth').value;
        document.getElementById('textSizeValue').textContent = document.getElementById('textSize').value;
        document.getElementById('imageWidthValue').textContent = document.getElementById('imageWidth').value;
        document.getElementById('imageHeightValue').textContent = document.getElementById('imageHeight').value;
    }

    switchSignatureType(type) {
        this.currentSignatureType = type;

        // Hide all signature panels
        document.getElementById('drawSignature').classList.add('hidden');
        document.getElementById('textSignature').classList.add('hidden');
        document.getElementById('imageSignature').classList.add('hidden');

        // Show selected panel
        document.getElementById(type + 'Signature').classList.remove('hidden');
    }

    updateTextPreview() {
        const text = document.getElementById('signatureText').value || 'Your Name';
        const font = document.getElementById('signatureFont').value;
        const size = document.getElementById('textSize').value;
        const color = document.getElementById('textColor').value;

        const preview = document.getElementById('textPreview').querySelector('span');
        preview.textContent = text;
        preview.style.fontFamily = font;
        preview.style.fontSize = size + 'px';
        preview.style.color = color;
    }

    handleImageSelect(file) {
        if (!file || !file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = document.getElementById('previewImg');
            img.src = e.target.result;
            document.getElementById('imagePreview').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }

    async handleFileSelect(file) {
        if (!file || file.type !== 'application/pdf') {
            alert('Please select a valid PDF file.');
            return;
        }

        this.selectedFile = file;

        // Update file info
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = (file.size / 1024 / 1024).toFixed(2) + ' MB';
        document.getElementById('fileInfo').classList.remove('hidden');

        // Initialize PDF viewer
        await this.initializePDFViewer(file);
    }

    async initializePDFViewer(file) {
        try {
            // Destroy existing viewer if any
            if (this.pdfViewer) {
                this.pdfViewer.destroy();
            }

            // Create new PDF viewer
            this.pdfViewer = new PDFViewer('pdfViewer', {
                showThumbnails: true,
                allowSelection: true
            });

            // Load PDF
            await this.pdfViewer.loadPDF(file);

            // Add click handler for signature placement
            this.setupSignaturePlacement();

        } catch (error) {
            console.error('Error initializing PDF viewer:', error);
            alert('Error loading PDF: ' + error.message);
        }
    }

    setupSignaturePlacement() {
        const canvas = document.getElementById('pdfCanvas');
        if (!canvas) return;

        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;

            this.placeSignatureAt(x, y);
        });
    }

    placeSignatureAt(x, y) {
        // Clear existing previews
        this.clearSignaturePreviews();

        // Create signature preview at clicked position
        const preview = this.createSignaturePreview(x, y);
        if (preview) {
            this.signaturePreviews.push({
                x, y,
                element: preview,
                page: this.pdfViewer.getCurrentPage()
            });
        }
    }

    createSignaturePreview(x, y) {
        const canvas = document.getElementById('pdfCanvas');
        if (!canvas) return null;

        const container = canvas.parentElement;
        const preview = document.createElement('div');
        preview.className = 'signature-preview';

        // Position the preview
        const rect = canvas.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        const left = (rect.left - containerRect.left) + (x * rect.width);
        const top = (rect.top - containerRect.top) + (y * rect.height);

        preview.style.left = left + 'px';
        preview.style.top = top + 'px';

        // Set content based on signature type
        this.updateSignaturePreview(preview);

        container.appendChild(preview);
        return preview;
    }

    updateSignaturePreview(preview) {
        switch (this.currentSignatureType) {
            case 'draw':
                if (this.savedSignature) {
                    const img = document.createElement('img');
                    img.src = this.savedSignature;
                    img.style.width = '150px';
                    img.style.height = '75px';
                    preview.innerHTML = '';
                    preview.appendChild(img);
                } else {
                    preview.innerHTML = '<div class="p-2 text-xs text-gray-500">Draw signature first</div>';
                }
                break;
            case 'text':
                const text = document.getElementById('signatureText').value || 'Your Name';
                const font = document.getElementById('signatureFont').value;
                const size = document.getElementById('textSize').value;
                const color = document.getElementById('textColor').value;

                preview.innerHTML = text;
                preview.style.fontFamily = font;
                preview.style.fontSize = (size * 0.8) + 'px'; // Scale down for preview
                preview.style.color = color;
                preview.style.padding = '4px 8px';
                break;
            case 'image':
                const imageInput = document.getElementById('signatureImageInput');
                const width = document.getElementById('imageWidth').value;
                const height = document.getElementById('imageHeight').value;

                if (imageInput.files && imageInput.files[0]) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.style.width = (width * 0.8) + 'px'; // Scale down for preview
                        img.style.height = (height * 0.8) + 'px';
                        preview.innerHTML = '';
                        preview.appendChild(img);
                    };
                    reader.readAsDataURL(imageInput.files[0]);
                } else {
                    preview.innerHTML = '<div class="p-2 text-xs text-gray-500">Select image first</div>';
                }
                break;
        }

        // Add additional info if enabled
        this.addAdditionalInfo(preview);
    }

    addAdditionalInfo(preview) {
        let additionalInfo = '';

        if (document.getElementById('addDate').checked) {
            additionalInfo += new Date().toLocaleDateString() + ' ';
        }

        if (document.getElementById('addTime').checked) {
            additionalInfo += new Date().toLocaleTimeString() + ' ';
        }

        if (document.getElementById('addLocation').checked) {
            const location = document.getElementById('signatureLocation').value;
            if (location) {
                additionalInfo += location;
            }
        }

        if (additionalInfo) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'text-xs text-gray-600 mt-1';
            infoDiv.textContent = additionalInfo.trim();
            preview.appendChild(infoDiv);
        }
    }

    clearSignaturePreviews() {
        this.signaturePreviews.forEach(preview => {
            if (preview.element && preview.element.parentNode) {
                preview.element.parentNode.removeChild(preview.element);
            }
        });
        this.signaturePreviews = [];
    }

    previewSignature() {
        if (!this.selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }

        if (this.signaturePreviews.length === 0) {
            alert('Please click on the PDF where you want to place your signature.');
            return;
        }

        alert('Signature preview is shown on the PDF. Click "Apply Signature" to save the changes.');
    }

    async applySignature() {
        if (!this.selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }

        if (this.signaturePreviews.length === 0) {
            alert('Please place a signature first by clicking on the PDF.');
            return;
        }

        try {
            this.showProgress(true);

            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('signature_type', this.currentSignatureType);

            // Add signature positions
            formData.append('signatures', JSON.stringify(this.signaturePreviews.map(s => ({
                x: s.x,
                y: s.y,
                page: s.page
            }))));

            // Add signature data based on type
            if (this.currentSignatureType === 'draw' && this.savedSignature) {
                formData.append('signature_image', this.savedSignature);
            } else if (this.currentSignatureType === 'text') {
                formData.append('signature_text', document.getElementById('signatureText').value);
                formData.append('font_family', document.getElementById('signatureFont').value);
                formData.append('font_size', document.getElementById('textSize').value);
                formData.append('text_color', document.getElementById('textColor').value);
            } else if (this.currentSignatureType === 'image') {
                const imageFile = document.getElementById('signatureImageInput').files[0];
                if (imageFile) {
                    formData.append('signature_image_file', imageFile);
                }
                formData.append('image_width', document.getElementById('imageWidth').value);
                formData.append('image_height', document.getElementById('imageHeight').value);
            }

            // Add additional info
            formData.append('add_date', document.getElementById('addDate').checked);
            formData.append('add_time', document.getElementById('addTime').checked);
            formData.append('add_location', document.getElementById('addLocation').checked);
            if (document.getElementById('addLocation').checked) {
                formData.append('location', document.getElementById('signatureLocation').value);
            }

            const response = await fetch('/api/sign_pdf', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const blob = await response.blob();
                this.showProgress(false);
                this.showResult(blob);
            } else {
                throw new Error('Signature application failed');
            }

        } catch (error) {
            this.showProgress(false);
            alert('Error applying signature: ' + error.message);
        }
    }

    showProgress(show) {
        document.getElementById('progressModal').classList.toggle('hidden', !show);
    }

    showResult(blob) {
        document.getElementById('resultModal').classList.remove('hidden');

        // Setup download
        const url = window.URL.createObjectURL(blob);
        document.getElementById('downloadBtn').onclick = () => {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'signed.pdf';
            a.click();
        };
    }

    resetTool() {
        // Reset file selection
        this.selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');

        // Reset signature previews
        this.clearSignaturePreviews();

        // Reset PDF viewer
        if (this.pdfViewer) {
            this.pdfViewer.destroy();
            this.pdfViewer = null;
        }

        // Reset viewer container
        document.getElementById('pdfViewer').innerHTML = `
            <div class="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-pdf text-6xl mb-4"></i>
                    <p>Upload a PDF file to start signing</p>
                </div>
            </div>
        `;

        // Clear signature canvas
        this.clearCanvas();
        this.savedSignature = null;

        // Reset form values
        document.getElementById('signatureText').value = '';
        document.getElementById('signatureImageInput').value = '';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('signatureLocation').value = '';

        // Hide modals
        document.getElementById('resultModal').classList.add('hidden');
        document.getElementById('progressModal').classList.add('hidden');
    }
}

// Initialize the signature tool when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SignatureTool();
});
</script>
{% endblock %}
