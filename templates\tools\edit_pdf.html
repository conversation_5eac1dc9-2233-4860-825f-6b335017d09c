{% extends "base.html" %}

{% block title %}Edit PDF - {{ t.site_name }}{% endblock %}

{% block extra_head %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script src="{{ url_for('static', filename='js/pdf-viewer.js') }}"></script>
<style>
.edit-toolbar {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.tool-button {
    @apply px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors;
}

.tool-button.active {
    @apply bg-blue-600 text-white border-blue-600;
}

.pdf-editor {
    position: relative;
    height: 600px;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
}

.edit-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

.edit-element {
    position: absolute;
    border: 2px dashed #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    cursor: move;
    pointer-events: all;
}

.edit-element.selected {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.edit-element .resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border: 1px solid white;
    border-radius: 50%;
}

.edit-element .resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
.edit-element .resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
.edit-element .resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.edit-element .resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }

.text-editor {
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    overflow: hidden;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-edit text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Edit PDF</h1>
        <p class="text-xl text-gray-600">Edit text, add images, and modify PDF documents</p>
    </div>
    
    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Left Panel - Tools -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Upload Area -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Upload PDF</h2>
                <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-all duration-300">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-4">Drag & drop PDF or click to select</p>
                    <input type="file" id="fileInput" accept=".pdf" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-4 py-2 rounded-lg">
                        Select PDF File
                    </button>
                </div>
                
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                            <div>
                                <p id="fileName" class="font-medium text-gray-800"></p>
                                <p id="fileSize" class="text-sm text-gray-500"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Edit Tools -->
            <div id="editTools" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Edit Tools</h2>
                <div class="space-y-3">
                    <button id="selectTool" class="tool-button w-full text-left active">
                        <i class="fas fa-mouse-pointer mr-2"></i>Select
                    </button>
                    <button id="textTool" class="tool-button w-full text-left">
                        <i class="fas fa-font mr-2"></i>Add Text
                    </button>
                    <button id="imageTool" class="tool-button w-full text-left">
                        <i class="fas fa-image mr-2"></i>Add Image
                    </button>
                    <button id="shapeTool" class="tool-button w-full text-left">
                        <i class="fas fa-shapes mr-2"></i>Add Shape
                    </button>
                    <button id="highlightTool" class="tool-button w-full text-left">
                        <i class="fas fa-highlighter mr-2"></i>Highlight
                    </button>
                    <button id="eraseTool" class="tool-button w-full text-left">
                        <i class="fas fa-eraser mr-2"></i>Erase
                    </button>
                </div>
            </div>
            
            <!-- Text Properties -->
            <div id="textProperties" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Text Properties</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Font Family</label>
                        <select id="fontFamily" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="Arial">Arial</option>
                            <option value="Times New Roman">Times New Roman</option>
                            <option value="Courier New">Courier New</option>
                            <option value="Helvetica">Helvetica</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                        <input type="range" id="fontSize" min="8" max="72" value="14" class="w-full">
                        <div class="text-center text-sm text-gray-600"><span id="fontSizeValue">14</span>px</div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Text Color</label>
                        <input type="color" id="textColor" value="#000000" class="w-full h-8 border border-gray-300 rounded">
                    </div>
                    
                    <div class="flex space-x-2">
                        <button id="boldBtn" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-bold"></i>
                        </button>
                        <button id="italicBtn" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-italic"></i>
                        </button>
                        <button id="underlineBtn" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-underline"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Image Properties -->
            <div id="imageProperties" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Image Properties</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
                        <input type="file" id="imageInput" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Opacity</label>
                        <input type="range" id="imageOpacity" min="0" max="100" value="100" class="w-full">
                        <div class="text-center text-sm text-gray-600"><span id="imageOpacityValue">100</span>%</div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div id="editActions" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <div class="space-y-3">
                    <button id="undoBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        <i class="fas fa-undo mr-2"></i>Undo
                    </button>
                    <button id="redoBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        <i class="fas fa-redo mr-2"></i>Redo
                    </button>
                    <button id="deleteBtn" class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700" disabled>
                        <i class="fas fa-trash mr-2"></i>Delete Selected
                    </button>
                    <hr>
                    <button id="saveBtn" class="w-full btn-primary text-white px-4 py-2 rounded-lg font-semibold">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Right Panel - PDF Editor -->
        <div class="lg:col-span-3">
            <div class="bg-white rounded-xl shadow-lg">
                <!-- Toolbar -->
                <div id="editorToolbar" class="edit-toolbar p-4 hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="text-sm text-gray-600">
                                Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
                            </div>
                            <div class="flex space-x-2">
                                <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button id="zoomOutBtn" class="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoomLevel" class="text-sm text-gray-600">100%</span>
                            <button id="zoomInBtn" class="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Editor Canvas -->
                <div class="p-6">
                    <div id="pdfEditor" class="pdf-editor">
                        <div class="flex items-center justify-center h-full bg-gray-100 rounded-lg">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-file-pdf text-6xl mb-4"></i>
                                <p>Upload a PDF file to start editing</p>
                            </div>
                        </div>
                        
                        <!-- Edit Overlay -->
                        <div id="editOverlay" class="edit-overlay"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Progress Modal -->
    <div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-red-200 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Processing</h3>
                <p class="text-gray-600">Saving your PDF edits...</p>
            </div>
        </div>
    </div>
    
    <!-- Result Modal -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">PDF Edited!</h3>
                <p class="text-gray-600 mb-6">Your PDF has been successfully edited.</p>
                
                <div class="flex justify-center space-x-4">
                    <button id="downloadBtn" class="btn-primary text-white px-6 py-2 rounded-lg font-semibold">
                        <i class="fas fa-download mr-2"></i>Download
                    </button>
                    <button id="newEditBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        Edit Another PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class PDFEditor {
    constructor() {
        this.pdfViewer = null;
        this.selectedFile = null;
        this.currentTool = 'select';
        this.editElements = [];
        this.selectedElement = null;
        this.history = [];
        this.historyIndex = -1;
        this.zoom = 1.0;
        this.currentPage = 1;
        this.totalPages = 1;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateSliderValues();
    }
    
    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                this.handleFileSelect(files[0]);
            }
        });
        
        // Tool buttons
        document.querySelectorAll('.tool-button').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectTool(e.target.closest('.tool-button').id.replace('Tool', '')));
        });
        
        // Property controls
        document.getElementById('fontSize').addEventListener('input', (e) => {
            document.getElementById('fontSizeValue').textContent = e.target.value;
            this.updateSelectedElement();
        });
        
        document.getElementById('imageOpacity').addEventListener('input', (e) => {
            document.getElementById('imageOpacityValue').textContent = e.target.value;
            this.updateSelectedElement();
        });
        
        // Style buttons
        document.getElementById('boldBtn').addEventListener('click', () => this.toggleStyle('bold'));
        document.getElementById('italicBtn').addEventListener('click', () => this.toggleStyle('italic'));
        document.getElementById('underlineBtn').addEventListener('click', () => this.toggleStyle('underline'));
        
        // Navigation
        document.getElementById('prevPageBtn').addEventListener('click', () => this.previousPage());
        document.getElementById('nextPageBtn').addEventListener('click', () => this.nextPage());
        document.getElementById('zoomInBtn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOutBtn').addEventListener('click', () => this.zoomOut());
        
        // Actions
        document.getElementById('undoBtn').addEventListener('click', () => this.undo());
        document.getElementById('redoBtn').addEventListener('click', () => this.redo());
        document.getElementById('deleteBtn').addEventListener('click', () => this.deleteSelected());
        document.getElementById('saveBtn').addEventListener('click', () => this.saveChanges());
        document.getElementById('newEditBtn').addEventListener('click', () => this.resetEditor());
        
        // Image upload
        document.getElementById('imageInput').addEventListener('change', (e) => this.handleImageUpload(e.target.files[0]));
        
        // Editor canvas events
        document.getElementById('pdfEditor').addEventListener('click', (e) => this.handleCanvasClick(e));
        document.getElementById('pdfEditor').addEventListener('dblclick', (e) => this.handleCanvasDoubleClick(e));
    }
    
    updateSliderValues() {
        document.getElementById('fontSizeValue').textContent = document.getElementById('fontSize').value;
        document.getElementById('imageOpacityValue').textContent = document.getElementById('imageOpacity').value;
    }
    
    async handleFileSelect(file) {
        if (!file || file.type !== 'application/pdf') {
            alert('Please select a valid PDF file.');
            return;
        }
        
        this.selectedFile = file;
        
        // Update file info
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = (file.size / 1024 / 1024).toFixed(2) + ' MB';
        document.getElementById('fileInfo').classList.remove('hidden');
        
        // Show edit tools
        document.getElementById('editTools').classList.remove('hidden');
        document.getElementById('editActions').classList.remove('hidden');
        document.getElementById('editorToolbar').classList.remove('hidden');
        
        // Initialize PDF viewer
        await this.initializePDFViewer(file);
    }
    
    async initializePDFViewer(file) {
        try {
            // Destroy existing viewer if any
            if (this.pdfViewer) {
                this.pdfViewer.destroy();
            }
            
            // Create new PDF viewer
            this.pdfViewer = new PDFViewer('pdfEditor', {
                showThumbnails: false,
                allowSelection: false,
                enableEditing: true
            });
            
            // Load PDF
            await this.pdfViewer.loadPDF(file);
            
            // Update page info
            this.totalPages = this.pdfViewer.getTotalPages();
            document.getElementById('totalPages').textContent = this.totalPages;
            
        } catch (error) {
            console.error('Error initializing PDF viewer:', error);
            alert('Error loading PDF: ' + error.message);
        }
    }
    
    selectTool(tool) {
        // Update active tool
        document.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
        document.getElementById(tool + 'Tool').classList.add('active');
        
        this.currentTool = tool;
        
        // Show/hide property panels
        document.getElementById('textProperties').classList.toggle('hidden', tool !== 'text');
        document.getElementById('imageProperties').classList.toggle('hidden', tool !== 'image');
        
        // Update cursor
        const editor = document.getElementById('pdfEditor');
        editor.style.cursor = this.getCursorForTool(tool);
    }
    
    getCursorForTool(tool) {
        switch (tool) {
            case 'select': return 'default';
            case 'text': return 'text';
            case 'image': return 'crosshair';
            case 'shape': return 'crosshair';
            case 'highlight': return 'crosshair';
            case 'erase': return 'crosshair';
            default: return 'default';
        }
    }
    
    handleCanvasClick(e) {
        const rect = e.target.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        if (this.currentTool === 'text') {
            this.addTextElement(x, y);
        } else if (this.currentTool === 'image' && document.getElementById('imageInput').files[0]) {
            this.addImageElement(x, y, document.getElementById('imageInput').files[0]);
        }
    }
    
    handleCanvasDoubleClick(e) {
        if (this.selectedElement && this.selectedElement.type === 'text') {
            this.editTextElement(this.selectedElement);
        }
    }
    
    addTextElement(x, y) {
        const element = {
            id: Date.now(),
            type: 'text',
            x: x,
            y: y,
            width: 200,
            height: 30,
            content: 'Double-click to edit',
            fontSize: parseInt(document.getElementById('fontSize').value),
            fontFamily: document.getElementById('fontFamily').value,
            color: document.getElementById('textColor').value,
            bold: false,
            italic: false,
            underline: false,
            page: this.currentPage
        };
        
        this.editElements.push(element);
        this.renderElement(element);
        this.selectElement(element);
        this.saveState();
    }
    
    addImageElement(x, y, file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const element = {
                id: Date.now(),
                type: 'image',
                x: x,
                y: y,
                width: 150,
                height: 100,
                src: e.target.result,
                opacity: parseInt(document.getElementById('imageOpacity').value) / 100,
                page: this.currentPage
            };
            
            this.editElements.push(element);
            this.renderElement(element);
            this.selectElement(element);
            this.saveState();
        };
        reader.readAsDataURL(file);
    }
    
    renderElement(element) {
        const overlay = document.getElementById('editOverlay');
        const div = document.createElement('div');
        div.className = 'edit-element';
        div.dataset.elementId = element.id;
        div.style.left = (element.x * this.zoom) + 'px';
        div.style.top = (element.y * this.zoom) + 'px';
        div.style.width = (element.width * this.zoom) + 'px';
        div.style.height = (element.height * this.zoom) + 'px';
        
        if (element.type === 'text') {
            div.innerHTML = `
                <div class="text-editor" style="
                    font-size: ${element.fontSize * this.zoom}px;
                    font-family: ${element.fontFamily};
                    color: ${element.color};
                    font-weight: ${element.bold ? 'bold' : 'normal'};
                    font-style: ${element.italic ? 'italic' : 'normal'};
                    text-decoration: ${element.underline ? 'underline' : 'none'};
                    width: 100%;
                    height: 100%;
                    padding: 4px;
                ">${element.content}</div>
            `;
        } else if (element.type === 'image') {
            div.innerHTML = `
                <img src="${element.src}" style="
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    opacity: ${element.opacity};
                ">
            `;
        }
        
        // Add resize handles
        div.innerHTML += `
            <div class="resize-handle nw"></div>
            <div class="resize-handle ne"></div>
            <div class="resize-handle sw"></div>
            <div class="resize-handle se"></div>
        `;
        
        // Add event listeners
        div.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });
        
        div.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            if (element.type === 'text') {
                this.editTextElement(element);
            }
        });
        
        overlay.appendChild(div);
    }
    
    selectElement(element) {
        // Deselect previous element
        if (this.selectedElement) {
            const prevDiv = document.querySelector(`[data-element-id="${this.selectedElement.id}"]`);
            if (prevDiv) prevDiv.classList.remove('selected');
        }
        
        // Select new element
        this.selectedElement = element;
        const div = document.querySelector(`[data-element-id="${element.id}"]`);
        if (div) div.classList.add('selected');
        
        // Enable delete button
        document.getElementById('deleteBtn').disabled = false;
        
        // Update property panels
        if (element.type === 'text') {
            document.getElementById('fontSize').value = element.fontSize;
            document.getElementById('fontFamily').value = element.fontFamily;
            document.getElementById('textColor').value = element.color;
            this.updateSliderValues();
        } else if (element.type === 'image') {
            document.getElementById('imageOpacity').value = element.opacity * 100;
            this.updateSliderValues();
        }
    }
    
    editTextElement(element) {
        const div = document.querySelector(`[data-element-id="${element.id}"]`);
        const textDiv = div.querySelector('.text-editor');
        
        const input = document.createElement('textarea');
        input.value = element.content;
        input.className = 'text-editor';
        input.style.cssText = textDiv.style.cssText;
        
        textDiv.replaceWith(input);
        input.focus();
        input.select();
        
        input.addEventListener('blur', () => {
            element.content = input.value;
            const newTextDiv = document.createElement('div');
            newTextDiv.className = 'text-editor';
            newTextDiv.style.cssText = input.style.cssText;
            newTextDiv.textContent = element.content;
            input.replaceWith(newTextDiv);
            this.saveState();
        });
        
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                input.blur();
            }
        });
    }
    
    updateSelectedElement() {
        if (!this.selectedElement) return;
        
        if (this.selectedElement.type === 'text') {
            this.selectedElement.fontSize = parseInt(document.getElementById('fontSize').value);
            this.selectedElement.fontFamily = document.getElementById('fontFamily').value;
            this.selectedElement.color = document.getElementById('textColor').value;
        } else if (this.selectedElement.type === 'image') {
            this.selectedElement.opacity = parseInt(document.getElementById('imageOpacity').value) / 100;
        }
        
        // Re-render element
        const div = document.querySelector(`[data-element-id="${this.selectedElement.id}"]`);
        if (div) {
            div.remove();
            this.renderElement(this.selectedElement);
            this.selectElement(this.selectedElement);
        }
    }
    
    toggleStyle(style) {
        if (!this.selectedElement || this.selectedElement.type !== 'text') return;
        
        this.selectedElement[style] = !this.selectedElement[style];
        
        // Update button appearance
        const btn = document.getElementById(style + 'Btn');
        btn.classList.toggle('bg-blue-600', this.selectedElement[style]);
        btn.classList.toggle('text-white', this.selectedElement[style]);
        
        this.updateSelectedElement();
        this.saveState();
    }
    
    deleteSelected() {
        if (!this.selectedElement) return;
        
        // Remove from elements array
        this.editElements = this.editElements.filter(el => el.id !== this.selectedElement.id);
        
        // Remove from DOM
        const div = document.querySelector(`[data-element-id="${this.selectedElement.id}"]`);
        if (div) div.remove();
        
        this.selectedElement = null;
        document.getElementById('deleteBtn').disabled = true;
        this.saveState();
    }
    
    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            document.getElementById('currentPage').textContent = this.currentPage;
            if (this.pdfViewer) {
                this.pdfViewer.goToPage(this.currentPage);
            }
            this.updateElementsVisibility();
        }
    }
    
    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            document.getElementById('currentPage').textContent = this.currentPage;
            if (this.pdfViewer) {
                this.pdfViewer.goToPage(this.currentPage);
            }
            this.updateElementsVisibility();
        }
    }
    
    updateElementsVisibility() {
        this.editElements.forEach(element => {
            const div = document.querySelector(`[data-element-id="${element.id}"]`);
            if (div) {
                div.style.display = element.page === this.currentPage ? 'block' : 'none';
            }
        });
    }
    
    zoomIn() {
        this.zoom = Math.min(this.zoom * 1.2, 3.0);
        this.updateZoom();
    }
    
    zoomOut() {
        this.zoom = Math.max(this.zoom / 1.2, 0.5);
        this.updateZoom();
    }
    
    updateZoom() {
        document.getElementById('zoomLevel').textContent = Math.round(this.zoom * 100) + '%';
        if (this.pdfViewer) {
            this.pdfViewer.setZoom(this.zoom);
        }
        
        // Re-render all elements with new zoom
        document.getElementById('editOverlay').innerHTML = '';
        this.editElements.forEach(element => {
            if (element.page === this.currentPage) {
                this.renderElement(element);
            }
        });
    }
    
    saveState() {
        // Remove future history if we're not at the end
        this.history = this.history.slice(0, this.historyIndex + 1);
        
        // Add current state
        this.history.push(JSON.parse(JSON.stringify(this.editElements)));
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > 50) {
            this.history.shift();
            this.historyIndex--;
        }
    }
    
    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.editElements = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.refreshElements();
        }
    }
    
    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.editElements = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.refreshElements();
        }
    }
    
    refreshElements() {
        document.getElementById('editOverlay').innerHTML = '';
        this.editElements.forEach(element => {
            if (element.page === this.currentPage) {
                this.renderElement(element);
            }
        });
        this.selectedElement = null;
        document.getElementById('deleteBtn').disabled = true;
    }
    
    async saveChanges() {
        if (!this.selectedFile) {
            alert('No PDF file selected.');
            return;
        }
        
        try {
            document.getElementById('progressModal').classList.remove('hidden');
            
            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('edits', JSON.stringify(this.editElements));
            
            const response = await fetch('/api/edit_pdf', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                const blob = await response.blob();
                document.getElementById('progressModal').classList.add('hidden');
                this.showResult(blob);
            } else {
                throw new Error('PDF editing failed');
            }
            
        } catch (error) {
            document.getElementById('progressModal').classList.add('hidden');
            alert('Error saving PDF: ' + error.message);
        }
    }
    
    showResult(blob) {
        document.getElementById('resultModal').classList.remove('hidden');
        
        // Setup download
        const url = window.URL.createObjectURL(blob);
        document.getElementById('downloadBtn').onclick = () => {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'edited.pdf';
            a.click();
        };
    }
    
    resetEditor() {
        // Reset file selection
        this.selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        
        // Hide panels
        document.getElementById('editTools').classList.add('hidden');
        document.getElementById('textProperties').classList.add('hidden');
        document.getElementById('imageProperties').classList.add('hidden');
        document.getElementById('editActions').classList.add('hidden');
        document.getElementById('editorToolbar').classList.add('hidden');
        
        // Reset PDF viewer
        if (this.pdfViewer) {
            this.pdfViewer.destroy();
            this.pdfViewer = null;
        }
        
        // Reset editor
        document.getElementById('pdfEditor').innerHTML = `
            <div class="flex items-center justify-center h-full bg-gray-100 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-pdf text-6xl mb-4"></i>
                    <p>Upload a PDF file to start editing</p>
                </div>
            </div>
            <div id="editOverlay" class="edit-overlay"></div>
        `;
        
        // Reset state
        this.editElements = [];
        this.selectedElement = null;
        this.history = [];
        this.historyIndex = -1;
        this.zoom = 1.0;
        this.currentPage = 1;
        this.totalPages = 1;
        
        // Hide modals
        document.getElementById('resultModal').classList.add('hidden');
        document.getElementById('progressModal').classList.add('hidden');
        
        // Reset tool selection
        this.selectTool('select');
    }
    
    handleImageUpload(file) {
        if (file && file.type.startsWith('image/')) {
            // Image is ready to be placed on next click
            this.selectTool('image');
        }
    }
}

// Initialize the PDF editor when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new PDFEditor();
});
</script>
{% endblock %}
