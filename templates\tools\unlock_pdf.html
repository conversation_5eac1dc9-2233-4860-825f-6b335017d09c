{% extends "base.html" %}

{% block title %}Unlock PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-unlock text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Unlock PDF</h1>
        <p class="text-xl text-gray-600">Remove password protection from PDF files</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a password-protected PDF file</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Password Input -->
    <div id="passwordSection" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Enter PDF Password</h2>
        
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">PDF Password</label>
                <div class="relative">
                    <input type="password" id="pdfPassword" placeholder="Enter the PDF password" 
                           class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                    <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-500 mt-1">Enter the password used to protect this PDF</p>
            </div>
            
            <!-- Password Hints -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-yellow-800 mb-2">
                    <i class="fas fa-lightbulb mr-2"></i>Password Tips:
                </h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>• Check if Caps Lock is on</li>
                    <li>• Try common passwords you might have used</li>
                    <li>• Password is case-sensitive</li>
                    <li>• Make sure there are no extra spaces</li>
                </ul>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="testPasswordBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-key mr-2"></i>Test Password
            </button>
            <button id="unlockBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-unlock mr-2"></i>Unlock PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Removing password protection from your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully unlocked!</p>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-green-600 text-xl mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-green-800">Password Protection Removed</p>
                        <p class="text-sm text-green-700">The PDF is now accessible without a password</p>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newUnlockBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Unlock Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-green-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-green-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-green-700">
            <li>Upload your password-protected PDF file</li>
            <li>Enter the correct password for the PDF</li>
            <li>Test the password to verify it's correct</li>
            <li>Unlock the PDF to remove password protection</li>
            <li>Download your unlocked PDF file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-red-800 text-sm">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <strong>Important:</strong> You must know the correct password to unlock the PDF. 
                This tool cannot crack or guess passwords - it only removes protection when the correct password is provided.
            </p>
        </div>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-shield-alt mr-2"></i>
                <strong>Privacy:</strong> Your PDF and password are processed securely and are not stored on our servers.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Password visibility toggle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('pdfPassword');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Enter key in password field
        document.getElementById('pdfPassword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                unlockPDF();
            }
        });
        
        // Buttons
        document.getElementById('testPasswordBtn').addEventListener('click', testPassword);
        document.getElementById('unlockBtn').addEventListener('click', unlockPDF);
        document.getElementById('newUnlockBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('passwordSection').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div class="flex-1">
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <i class="fas fa-lock mr-1"></i>
                        Password Protected
                    </span>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function testPassword() {
        const password = document.getElementById('pdfPassword').value;
        if (!password.trim()) {
            alert('Please enter a password first.');
            return;
        }
        
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('password', password);
        
        fetch('/api/test_pdf_password', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ Password is correct! You can now unlock the PDF.');
            } else {
                alert('❌ Incorrect password. Please try again.');
            }
        })
        .catch(error => {
            alert('Error testing password: ' + error.message);
        });
    }
    
    function unlockPDF() {
        const password = document.getElementById('pdfPassword').value;
        if (!password.trim()) {
            alert('Please enter the PDF password.');
            return;
        }
        
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('password', password);
        
        // Show progress
        document.getElementById('passwordSection').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/unlock_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                return response.json().then(data => {
                    throw new Error(data.error || 'Unlock failed');
                });
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'unlocked.pdf';
                a.click();
            };
        })
        .catch(error => {
            let errorMessage = error.message;
            if (errorMessage.includes('password') || errorMessage.includes('incorrect')) {
                errorMessage = 'Incorrect password. Please check your password and try again.';
            }
            
            alert('Error unlocking PDF: ' + errorMessage);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('passwordSection').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('passwordSection').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset password field
        document.getElementById('pdfPassword').value = '';
        document.getElementById('pdfPassword').type = 'password';
        document.getElementById('togglePassword').querySelector('i').classList.remove('fa-eye-slash');
        document.getElementById('togglePassword').querySelector('i').classList.add('fa-eye');
    }
</script>
{% endblock %}
