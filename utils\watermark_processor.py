import fitz  # PyMuPDF
from PIL import Image, ImageDraw, ImageFont
import io
import os
import tempfile
import uuid
import math
from reportlab.pdfgen import canvas
from reportlab.lib.colors import Color
from reportlab.lib.utils import ImageReader

class WatermarkProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        
        # Predefined stamps with colors
        self.stamps = {
            'APPROVED': {'color': (0, 0.8, 0), 'type': 'success'},
            'CERTIFIED': {'color': (0, 0.8, 0), 'type': 'success'},
            'ORIGINAL': {'color': (0, 0.8, 0), 'type': 'success'},
            'PASS': {'color': (0, 0.8, 0), 'type': 'success'},
            'PAID': {'color': (0, 0.8, 0), 'type': 'success'},
            
            'REJECTED': {'color': (0.8, 0, 0), 'type': 'error'},
            'CANCELED': {'color': (0.8, 0, 0), 'type': 'error'},
            'DO_NOT_COPY': {'color': (0.8, 0, 0), 'type': 'error'},
            
            'PENDING': {'color': (1, 0.6, 0), 'type': 'warning'},
            'URGENT': {'color': (1, 0.6, 0), 'type': 'warning'},
            'SAMPLE': {'color': (1, 0.6, 0), 'type': 'warning'},
            
            'CONFIDENTIAL': {'color': (0.5, 0, 0.5), 'type': 'info'},
            'CLASSIFIED': {'color': (0.5, 0, 0.5), 'type': 'info'},
            'TOP_SECRET': {'color': (0.5, 0, 0.5), 'type': 'info'},
            'COPYRIGHT': {'color': (0.3, 0.3, 0.3), 'type': 'neutral'},
            'COPY': {'color': (0.3, 0.3, 0.3), 'type': 'neutral'}
        }
    
    def add_text_watermark(self, file, text, font_size=50, opacity=0.5, color=(0, 0, 0), 
                          rotation=45, position='center', pages='all'):
        """Add text watermark to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Parse pages
        if pages == 'all':
            page_list = list(range(pdf_doc.page_count))
        else:
            page_list = [int(p) - 1 for p in pages.split(',') if p.strip().isdigit()]
        
        for page_num in page_list:
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                page_rect = page.rect
                
                # Calculate position
                x, y = self._calculate_position(position, page_rect, text, font_size)
                
                # Create text annotation
                text_color = (color[0], color[1], color[2])
                
                # Insert text with rotation
                page.insert_text(
                    (x, y), 
                    text, 
                    fontsize=font_size,
                    color=text_color,
                    rotate=rotation,
                    overlay=True
                )
                
                # Apply opacity by creating a semi-transparent overlay
                if opacity < 1.0:
                    self._apply_opacity(page, opacity)
        
        output_path = os.path.join(self.temp_dir, f'text_watermark_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_image_watermark(self, file, image_file, scale=0.5, opacity=0.5, 
                           position='center', rotation=0, pages='all'):
        """Add image watermark to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Process watermark image
        img_data = image_file.read()
        img = Image.open(io.BytesIO(img_data))
        
        # Apply opacity to image
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Apply opacity
        alpha = img.split()[-1]
        alpha = alpha.point(lambda p: int(p * opacity))
        img.putalpha(alpha)
        
        # Rotate image if needed
        if rotation != 0:
            img = img.rotate(rotation, expand=True)
        
        # Save processed image
        watermark_path = os.path.join(self.temp_dir, f'watermark_{uuid.uuid4().hex}.png')
        img.save(watermark_path)
        
        # Parse pages
        if pages == 'all':
            page_list = list(range(pdf_doc.page_count))
        else:
            page_list = [int(p) - 1 for p in pages.split(',') if p.strip().isdigit()]
        
        for page_num in page_list:
            if page_num < pdf_doc.page_count:
                page = pdf_doc[page_num]
                page_rect = page.rect
                
                # Calculate dimensions
                img_width, img_height = img.size
                scaled_width = img_width * scale
                scaled_height = img_height * scale
                
                # Calculate position
                x, y = self._calculate_image_position(position, page_rect, scaled_width, scaled_height)
                
                # Insert image
                img_rect = fitz.Rect(x, y, x + scaled_width, y + scaled_height)
                page.insert_image(img_rect, filename=watermark_path)
        
        output_path = os.path.join(self.temp_dir, f'image_watermark_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_stamp(self, file, stamp_text, opacity=0.7, scale=1.0, position='center', 
                  rotation=0, pages='all'):
        """Add predefined stamp to PDF"""
        if stamp_text not in self.stamps:
            raise ValueError(f"Unknown stamp: {stamp_text}")
        
        stamp_info = self.stamps[stamp_text]
        color = stamp_info['color']
        
        # Create stamp image
        stamp_image = self._create_stamp_image(stamp_text, color, scale)
        
        # Convert PIL image to bytes for watermark processing
        img_bytes = io.BytesIO()
        stamp_image.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        # Create a file-like object
        class FileWrapper:
            def __init__(self, data):
                self.data = data
            def read(self):
                return self.data
        
        image_file = FileWrapper(img_bytes.getvalue())
        
        return self.add_image_watermark(file, image_file, scale=1.0, opacity=opacity,
                                      position=position, rotation=rotation, pages=pages)
    
    def _create_stamp_image(self, text, color, scale=1.0):
        """Create stamp image with border"""
        # Base dimensions
        base_width = 300
        base_height = 100
        
        width = int(base_width * scale)
        height = int(base_height * scale)
        
        # Create image
        img = Image.new('RGBA', (width, height), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # Convert color to RGB (0-255 range)
        rgb_color = tuple(int(c * 255) for c in color)
        
        # Draw border
        border_width = max(3, int(5 * scale))
        draw.rectangle([0, 0, width-1, height-1], outline=rgb_color, width=border_width)
        
        # Try to load font
        try:
            font_size = int(24 * scale)
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font_size = int(24 * scale)
                font = ImageFont.load_default()
            except:
                font = None
        
        # Draw text
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text) * 10 * scale
            text_height = 20 * scale
        
        text_x = (width - text_width) // 2
        text_y = (height - text_height) // 2
        
        draw.text((text_x, text_y), text, fill=rgb_color, font=font)
        
        return img
    
    def _calculate_position(self, position, page_rect, text, font_size):
        """Calculate text position on page"""
        page_width = page_rect.width
        page_height = page_rect.height
        
        # Estimate text dimensions
        text_width = len(text) * font_size * 0.6
        text_height = font_size
        
        if position == 'center':
            x = (page_width - text_width) / 2
            y = page_height / 2
        elif position == 'top-left':
            x = 50
            y = 50
        elif position == 'top-right':
            x = page_width - text_width - 50
            y = 50
        elif position == 'bottom-left':
            x = 50
            y = page_height - 50
        elif position == 'bottom-right':
            x = page_width - text_width - 50
            y = page_height - 50
        elif position == 'top-center':
            x = (page_width - text_width) / 2
            y = 50
        elif position == 'bottom-center':
            x = (page_width - text_width) / 2
            y = page_height - 50
        else:  # center
            x = (page_width - text_width) / 2
            y = page_height / 2
        
        return x, y
    
    def _calculate_image_position(self, position, page_rect, img_width, img_height):
        """Calculate image position on page"""
        page_width = page_rect.width
        page_height = page_rect.height
        
        if position == 'center':
            x = (page_width - img_width) / 2
            y = (page_height - img_height) / 2
        elif position == 'top-left':
            x = 50
            y = 50
        elif position == 'top-right':
            x = page_width - img_width - 50
            y = 50
        elif position == 'bottom-left':
            x = 50
            y = page_height - img_height - 50
        elif position == 'bottom-right':
            x = page_width - img_width - 50
            y = page_height - img_height - 50
        elif position == 'top-center':
            x = (page_width - img_width) / 2
            y = 50
        elif position == 'bottom-center':
            x = (page_width - img_width) / 2
            y = page_height - img_height - 50
        else:  # center
            x = (page_width - img_width) / 2
            y = (page_height - img_height) / 2
        
        return x, y
    
    def _apply_opacity(self, page, opacity):
        """Apply opacity to page elements (simplified approach)"""
        # This is a simplified approach - in practice, you might need more sophisticated opacity handling
        pass
    
    def get_available_stamps(self):
        """Get list of available stamps"""
        return list(self.stamps.keys())
