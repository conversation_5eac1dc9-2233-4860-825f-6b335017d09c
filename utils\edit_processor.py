import os
import tempfile
import fitz  # PyMuPDF
from PIL import Image
import base64
from io import BytesIO
import json

class EditProcessor:
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def apply_edits(self, file, edits):
        """Apply edits to PDF file"""
        try:
            # Save uploaded file temporarily
            temp_input_path = os.path.join(self.temp_dir, f'input_{os.urandom(8).hex()}.pdf')
            file.save(temp_input_path)
            
            # Open PDF document
            doc = fitz.open(temp_input_path)
            
            # Group edits by page
            edits_by_page = {}
            for edit in edits:
                page_num = edit.get('page', 1) - 1  # Convert to 0-based index
                if page_num not in edits_by_page:
                    edits_by_page[page_num] = []
                edits_by_page[page_num].append(edit)
            
            # Apply edits to each page
            for page_num, page_edits in edits_by_page.items():
                if page_num < len(doc):
                    page = doc[page_num]
                    self.apply_page_edits(page, page_edits)
            
            # Save edited PDF
            output_path = os.path.join(self.temp_dir, f'edited_{os.urandom(8).hex()}.pdf')
            doc.save(output_path)
            doc.close()
            
            # Clean up input file
            os.unlink(temp_input_path)
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Error applying edits: {str(e)}")
    
    def apply_page_edits(self, page, edits):
        """Apply edits to a specific page"""
        try:
            for edit in edits:
                edit_type = edit.get('type')
                
                if edit_type == 'text':
                    self.add_text_to_page(page, edit)
                elif edit_type == 'image':
                    self.add_image_to_page(page, edit)
                elif edit_type == 'shape':
                    self.add_shape_to_page(page, edit)
                elif edit_type == 'highlight':
                    self.add_highlight_to_page(page, edit)
                elif edit_type == 'erase':
                    self.erase_from_page(page, edit)
                    
        except Exception as e:
            raise Exception(f"Error applying page edits: {str(e)}")
    
    def add_text_to_page(self, page, edit):
        """Add text element to page"""
        try:
            # Get text properties
            x = edit.get('x', 0)
            y = edit.get('y', 0)
            width = edit.get('width', 200)
            height = edit.get('height', 30)
            content = edit.get('content', '')
            font_size = edit.get('fontSize', 12)
            font_family = edit.get('fontFamily', 'helvetica')
            color = self.hex_to_rgb(edit.get('color', '#000000'))
            bold = edit.get('bold', False)
            italic = edit.get('italic', False)
            
            # Create text rectangle
            rect = fitz.Rect(x, y, x + width, y + height)
            
            # Determine font
            font = self.get_font_name(font_family, bold, italic)
            
            # Insert text
            page.insert_textbox(
                rect,
                content,
                fontsize=font_size,
                fontname=font,
                color=color,
                align=0  # Left align
            )
            
        except Exception as e:
            raise Exception(f"Error adding text: {str(e)}")
    
    def add_image_to_page(self, page, edit):
        """Add image element to page"""
        try:
            # Get image properties
            x = edit.get('x', 0)
            y = edit.get('y', 0)
            width = edit.get('width', 150)
            height = edit.get('height', 100)
            src = edit.get('src', '')
            opacity = edit.get('opacity', 1.0)
            
            if not src or not src.startswith('data:image'):
                return
            
            # Decode base64 image
            image_data = src.split(',')[1]
            image_bytes = base64.b64decode(image_data)
            
            # Save temporary image
            temp_image_path = os.path.join(self.temp_dir, f'temp_img_{os.urandom(8).hex()}.png')
            with open(temp_image_path, 'wb') as f:
                f.write(image_bytes)
            
            # Create image rectangle
            rect = fitz.Rect(x, y, x + width, y + height)
            
            # Insert image
            page.insert_image(rect, filename=temp_image_path, alpha=int(opacity * 255))
            
            # Clean up temp image
            os.unlink(temp_image_path)
            
        except Exception as e:
            raise Exception(f"Error adding image: {str(e)}")
    
    def add_shape_to_page(self, page, edit):
        """Add shape element to page"""
        try:
            # Get shape properties
            x = edit.get('x', 0)
            y = edit.get('y', 0)
            width = edit.get('width', 100)
            height = edit.get('height', 100)
            shape_type = edit.get('shapeType', 'rectangle')
            fill_color = self.hex_to_rgb(edit.get('fillColor', '#ffffff'))
            stroke_color = self.hex_to_rgb(edit.get('strokeColor', '#000000'))
            stroke_width = edit.get('strokeWidth', 1)
            
            # Create shape rectangle
            rect = fitz.Rect(x, y, x + width, y + height)
            
            if shape_type == 'rectangle':
                page.draw_rect(rect, color=stroke_color, fill=fill_color, width=stroke_width)
            elif shape_type == 'circle':
                # Draw circle inscribed in rectangle
                center = rect.center
                radius = min(width, height) / 2
                page.draw_circle(center, radius, color=stroke_color, fill=fill_color, width=stroke_width)
            elif shape_type == 'line':
                # Draw line from top-left to bottom-right
                page.draw_line(rect.tl, rect.br, color=stroke_color, width=stroke_width)
                
        except Exception as e:
            raise Exception(f"Error adding shape: {str(e)}")
    
    def add_highlight_to_page(self, page, edit):
        """Add highlight to page"""
        try:
            # Get highlight properties
            x = edit.get('x', 0)
            y = edit.get('y', 0)
            width = edit.get('width', 100)
            height = edit.get('height', 20)
            color = self.hex_to_rgb(edit.get('color', '#ffff00'))
            opacity = edit.get('opacity', 0.5)
            
            # Create highlight rectangle
            rect = fitz.Rect(x, y, x + width, y + height)
            
            # Add highlight annotation
            highlight = page.add_highlight_annot(rect)
            highlight.set_colors(stroke=color)
            highlight.set_opacity(opacity)
            highlight.update()
            
        except Exception as e:
            raise Exception(f"Error adding highlight: {str(e)}")
    
    def erase_from_page(self, page, edit):
        """Erase content from page"""
        try:
            # Get erase area
            x = edit.get('x', 0)
            y = edit.get('y', 0)
            width = edit.get('width', 50)
            height = edit.get('height', 50)
            
            # Create erase rectangle
            rect = fitz.Rect(x, y, x + width, y + height)
            
            # Draw white rectangle to "erase" content
            page.draw_rect(rect, color=None, fill=(1, 1, 1), width=0)
            
        except Exception as e:
            raise Exception(f"Error erasing content: {str(e)}")
    
    def hex_to_rgb(self, hex_color):
        """Convert hex color to RGB tuple (0-1 range)"""
        try:
            hex_color = hex_color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16) / 255.0
                g = int(hex_color[2:4], 16) / 255.0
                b = int(hex_color[4:6], 16) / 255.0
                return (r, g, b)
            else:
                return (0, 0, 0)  # Default to black
        except:
            return (0, 0, 0)  # Default to black on error
    
    def get_font_name(self, font_family, bold=False, italic=False):
        """Get PyMuPDF font name"""
        font_map = {
            'Arial': 'helv',
            'Helvetica': 'helv',
            'Times New Roman': 'times',
            'Times': 'times',
            'Courier New': 'cour',
            'Courier': 'cour'
        }
        
        base_font = font_map.get(font_family, 'helv')
        
        if bold and italic:
            return base_font + 'bi'
        elif bold:
            return base_font + 'b'
        elif italic:
            return base_font + 'i'
        else:
            return base_font
    
    def get_text_bounds(self, page, text, font_size, font_name):
        """Get text bounding box"""
        try:
            # Create a temporary text to measure
            temp_rect = fitz.Rect(0, 0, 1000, 100)
            text_dict = {
                "text": text,
                "fontsize": font_size,
                "fontname": font_name
            }
            
            # Get text width
            width = fitz.get_text_length(text, fontname=font_name, fontsize=font_size)
            height = font_size * 1.2  # Approximate height
            
            return width, height
            
        except:
            # Fallback estimation
            return len(text) * font_size * 0.6, font_size * 1.2
    
    def validate_edit(self, edit):
        """Validate edit data"""
        required_fields = ['type', 'x', 'y', 'width', 'height']
        
        for field in required_fields:
            if field not in edit:
                return False, f"Missing required field: {field}"
        
        edit_type = edit.get('type')
        if edit_type not in ['text', 'image', 'shape', 'highlight', 'erase']:
            return False, f"Invalid edit type: {edit_type}"
        
        if edit_type == 'text' and not edit.get('content'):
            return False, "Text edit requires content"
        
        if edit_type == 'image' and not edit.get('src'):
            return False, "Image edit requires src"
        
        return True, "Valid"
    
    def optimize_edits(self, edits):
        """Optimize edits for better performance"""
        # Group overlapping edits
        # Remove duplicate edits
        # Sort by page and position
        
        optimized = []
        seen = set()
        
        for edit in edits:
            # Create a hash of the edit for deduplication
            edit_hash = hash(json.dumps(edit, sort_keys=True))
            if edit_hash not in seen:
                seen.add(edit_hash)
                optimized.append(edit)
        
        # Sort by page, then by y position, then by x position
        optimized.sort(key=lambda e: (e.get('page', 1), e.get('y', 0), e.get('x', 0)))
        
        return optimized
    
    def get_page_info(self, file):
        """Get information about PDF pages"""
        try:
            # Save uploaded file temporarily
            temp_path = os.path.join(self.temp_dir, f'temp_{os.urandom(8).hex()}.pdf')
            file.save(temp_path)
            
            doc = fitz.open(temp_path)
            
            pages_info = []
            for page_num in range(len(doc)):
                page = doc[page_num]
                rect = page.rect
                
                pages_info.append({
                    'page': page_num + 1,
                    'width': rect.width,
                    'height': rect.height,
                    'rotation': page.rotation
                })
            
            doc.close()
            os.unlink(temp_path)
            
            return pages_info
            
        except Exception as e:
            raise Exception(f"Error getting page info: {str(e)}")
