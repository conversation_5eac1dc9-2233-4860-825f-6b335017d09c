{% extends "base.html" %}

{% block title %}Add Page Numbers - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-list-ol text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Add Page Numbers</h1>
        <p class="text-xl text-gray-600">Add customizable page numbers to your PDF documents</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to add page numbers</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Page Number Options -->
    <div id="pageNumberOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Page Number Settings</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Position Settings -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Position</h3>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="radio" name="position" value="bottom-right" checked class="mr-2">
                        Bottom Right
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="position" value="bottom-left" class="mr-2">
                        Bottom Left
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="position" value="bottom-center" class="mr-2">
                        Bottom Center
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="position" value="top-right" class="mr-2">
                        Top Right
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="position" value="top-left" class="mr-2">
                        Top Left
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="position" value="top-center" class="mr-2">
                        Top Center
                    </label>
                </div>
            </div>
            
            <!-- Format Settings -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Format</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Number Format</label>
                        <select id="numberFormat" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">1, 2, 3...</option>
                            <option value="page-1">Page 1, Page 2...</option>
                            <option value="1-total">1 of 10, 2 of 10...</option>
                            <option value="page-1-total">Page 1 of 10...</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                        <input type="range" id="fontSize" min="8" max="24" value="12" class="w-full">
                        <div class="text-center text-sm text-gray-600 mt-1"><span id="fontSizeValue">12</span>pt</div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Start Page</label>
                        <input type="number" id="startPage" value="1" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview
            </button>
            <button id="addNumbersBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-list-ol mr-2"></i>Add Page Numbers
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Adding page numbers to your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Page numbers have been successfully added!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newNumberingBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Number Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-blue-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-blue-700">
            <li>Upload your PDF file</li>
            <li>Choose the position for page numbers</li>
            <li>Select the number format and font size</li>
            <li>Set the starting page number</li>
            <li>Preview the settings if needed</li>
            <li>Add page numbers and download your file</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Font size slider
        document.getElementById('fontSize').addEventListener('input', function() {
            document.getElementById('fontSizeValue').textContent = this.value;
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewNumbers);
        document.getElementById('addNumbersBtn').addEventListener('click', addPageNumbers);
        document.getElementById('newNumberingBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('pageNumberOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function previewNumbers() {
        const position = document.querySelector('input[name="position"]:checked').value;
        const format = document.getElementById('numberFormat').value;
        const fontSize = document.getElementById('fontSize').value;
        const startPage = document.getElementById('startPage').value;
        
        alert(`Preview Settings:
        
Position: ${position.replace('-', ' ')}
Format: ${document.getElementById('numberFormat').selectedOptions[0].text}
Font Size: ${fontSize}pt
Start Page: ${startPage}

Page numbers will be added to all pages starting from page ${startPage}.`);
    }
    
    function addPageNumbers() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('position', document.querySelector('input[name="position"]:checked').value);
        formData.append('format', document.getElementById('numberFormat').value);
        formData.append('font_size', document.getElementById('fontSize').value);
        formData.append('start_page', document.getElementById('startPage').value);
        
        // Show progress
        document.getElementById('pageNumberOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/add_page_numbers', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Failed to add page numbers');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'numbered.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error adding page numbers: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('pageNumberOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('pageNumberOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset form values
        document.querySelector('input[name="position"][value="bottom-right"]').checked = true;
        document.getElementById('numberFormat').value = '1';
        document.getElementById('fontSize').value = '12';
        document.getElementById('fontSizeValue').textContent = '12';
        document.getElementById('startPage').value = '1';
    }
</script>
{% endblock %}
