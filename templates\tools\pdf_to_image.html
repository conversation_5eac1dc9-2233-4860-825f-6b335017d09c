{% extends "base.html" %}

{% block title %}{{ t.tools_list.pdf_to_image.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-images text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.pdf_to_image.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.pdf_to_image.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to convert to images</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Conversion Options -->
    <div id="conversionOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Conversion Options</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Output Format</label>
                <select id="outputFormat" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="PNG" selected>PNG (Recommended)</option>
                    <option value="JPEG">JPEG</option>
                    <option value="BMP">BMP</option>
                    <option value="TIFF">TIFF</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Quality</label>
                <select id="quality" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="95" selected>High (95%)</option>
                    <option value="85">Medium (85%)</option>
                    <option value="75">Standard (75%)</option>
                    <option value="60">Low (60%)</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Resolution (DPI)</label>
                <select id="dpi" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="72">72 DPI (Web)</option>
                    <option value="150" selected>150 DPI (Standard)</option>
                    <option value="300">300 DPI (Print)</option>
                    <option value="600">600 DPI (High Quality)</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Page Range</label>
                <select id="pageRange" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all" selected>All Pages</option>
                    <option value="first">First Page Only</option>
                    <option value="custom">Custom Range</option>
                </select>
            </div>
        </div>
        
        <!-- Custom Page Range -->
        <div id="customRangeDiv" class="mb-6 hidden">
            <label class="block text-sm font-medium text-gray-700 mb-2">Custom Page Range</label>
            <input type="text" id="customRange" placeholder="e.g., 1-5, 8, 10-12" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <p class="text-sm text-gray-500 mt-1">Enter page numbers separated by commas. Use hyphens for ranges.</p>
        </div>
        
        <!-- Preview Options -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Preview & Output Options:</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="showPreview" checked class="mr-2">
                    Show image previews
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="zipOutput" checked class="mr-2">
                    Download as ZIP file
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="addPageNumbers" class="mr-2">
                    Add page numbers to filenames
                </label>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview First Page
            </button>
            <button id="convertBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-images mr-2"></i>Convert to Images
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Converting PDF pages to images...</p>
            
            <div class="w-full bg-gray-200 rounded-full h-2 mt-6">
                <div id="progressBar" class="bg-gradient-to-r from-orange-500 to-red-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center mb-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully converted to images!</p>
        </div>
        
        <!-- Image Previews -->
        <div id="imagePreviews" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
            <!-- Previews will be populated here -->
        </div>
        
        <!-- Download Options -->
        <div class="flex justify-center space-x-4">
            <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
            </button>
            <button id="newConversionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Convert Another PDF
            </button>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-orange-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-orange-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-orange-700">
            <li>Upload your PDF file using drag & drop or file selection</li>
            <li>Choose your preferred output format (PNG recommended for quality)</li>
            <li>Set the quality and resolution based on your needs</li>
            <li>Select which pages to convert (all pages or custom range)</li>
            <li>Click "Convert to Images" to start the conversion</li>
            <li>Download individual images or as a ZIP file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> Use PNG format for documents with text and graphics. Use JPEG for photo-heavy documents to reduce file size.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Page range selection
        document.getElementById('pageRange').addEventListener('change', function() {
            const customRangeDiv = document.getElementById('customRangeDiv');
            if (this.value === 'custom') {
                customRangeDiv.classList.remove('hidden');
            } else {
                customRangeDiv.classList.add('hidden');
            }
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewFirstPage);
        document.getElementById('convertBtn').addEventListener('click', convertToImages);
        document.getElementById('newConversionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('conversionOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function previewFirstPage() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        // This would require PDF.js implementation for client-side preview
        alert('Preview functionality would be implemented with PDF.js for client-side rendering.');
    }
    
    function convertToImages() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('format', document.getElementById('outputFormat').value);
        formData.append('quality', document.getElementById('quality').value);
        formData.append('dpi', document.getElementById('dpi').value);
        
        // Handle page range
        const pageRange = document.getElementById('pageRange').value;
        if (pageRange === 'custom') {
            const customRange = document.getElementById('customRange').value;
            if (!customRange.trim()) {
                alert('Please specify a custom page range.');
                return;
            }
            formData.append('pages', customRange);
        } else if (pageRange === 'first') {
            formData.append('pages', '1');
        }
        
        // Show progress
        document.getElementById('conversionOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        // Simulate progress
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const progressInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 300);
        
        fetch('/api/pdf_to_images', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Conversion failed');
            }
        })
        .then(blob => {
            setTimeout(() => {
                document.getElementById('progressSection').classList.add('hidden');
                document.getElementById('resultSection').classList.remove('hidden');
                
                // Setup download
                const url = window.URL.createObjectURL(blob);
                document.getElementById('downloadBtn').onclick = function() {
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'pdf_images.zip';
                    a.click();
                };
                
                // Show preview message (in a real implementation, you'd show actual image previews)
                const previews = document.getElementById('imagePreviews');
                previews.innerHTML = `
                    <div class="col-span-full text-center p-8 bg-gray-50 rounded-lg">
                        <i class="fas fa-images text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">Images are ready for download!</p>
                        <p class="text-sm text-gray-500 mt-2">Click the download button to get your converted images.</p>
                    </div>
                `;
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            alert('Error converting PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('conversionOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('conversionOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        document.getElementById('customRangeDiv').classList.add('hidden');
        document.getElementById('pageRange').value = 'all';
    }
</script>
{% endblock %}
