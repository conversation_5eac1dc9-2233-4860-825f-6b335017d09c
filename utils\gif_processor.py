import fitz  # PyMuPDF
from PIL import Image, ImageSequence
import io
import os
import tempfile
import uuid
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4, letter

class GifProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def gif_to_pdf(self, gif_file, layout='single_page', page_size='A4', quality=95):
        """Convert GIF animation to PDF"""
        gif_data = gif_file.read()
        gif = Image.open(io.BytesIO(gif_data))
        
        # Set page size
        if page_size == 'A4':
            page_size = A4
        else:
            page_size = letter
        
        page_width, page_height = page_size
        
        output_path = os.path.join(self.temp_dir, f'gif_to_pdf_{uuid.uuid4().hex}.pdf')
        
        if layout == 'single_page':
            # All frames on a single page
            c = canvas.Canvas(output_path, pagesize=page_size)
            
            frames = []
            try:
                for frame in ImageSequence.Iterator(gif):
                    frame_copy = frame.copy()
                    if frame_copy.mode != 'RGB':
                        frame_copy = frame_copy.convert('RGB')
                    frames.append(frame_copy)
            except Exception as e:
                print(f"Error processing GIF frames: {e}")
                return None
            
            if not frames:
                return None
            
            # Calculate grid layout
            frame_count = len(frames)
            cols = int(frame_count ** 0.5) + 1
            rows = (frame_count + cols - 1) // cols
            
            # Calculate frame size
            margin = 20
            available_width = page_width - 2 * margin
            available_height = page_height - 2 * margin
            
            frame_width = (available_width - (cols - 1) * 10) / cols
            frame_height = (available_height - (rows - 1) * 10) / rows
            
            # Maintain aspect ratio
            original_width, original_height = frames[0].size
            aspect_ratio = original_width / original_height
            
            if frame_width / frame_height > aspect_ratio:
                frame_width = frame_height * aspect_ratio
            else:
                frame_height = frame_width / aspect_ratio
            
            # Place frames
            for i, frame in enumerate(frames):
                row = i // cols
                col = i % cols
                
                x = margin + col * (frame_width + 10)
                y = page_height - margin - (row + 1) * (frame_height + 10)
                
                # Save frame as temporary image
                frame_path = os.path.join(self.temp_dir, f'frame_{i}_{uuid.uuid4().hex}.jpg')
                frame.save(frame_path, 'JPEG', quality=quality)
                
                # Draw frame
                c.drawImage(frame_path, x, y, width=frame_width, height=frame_height)
                
                # Add frame number
                c.setFont("Helvetica", 8)
                c.drawString(x, y - 12, f"Frame {i + 1}")
            
            c.save()
        
        else:  # separate_pages
            # Each frame on a separate page
            c = canvas.Canvas(output_path, pagesize=page_size)
            
            frame_number = 0
            try:
                for frame in ImageSequence.Iterator(gif):
                    frame_copy = frame.copy()
                    if frame_copy.mode != 'RGB':
                        frame_copy = frame_copy.convert('RGB')
                    
                    # Calculate size to fit page
                    original_width, original_height = frame_copy.size
                    
                    margin = 50
                    max_width = page_width - 2 * margin
                    max_height = page_height - 2 * margin
                    
                    scale_x = max_width / original_width
                    scale_y = max_height / original_height
                    scale = min(scale_x, scale_y)
                    
                    new_width = original_width * scale
                    new_height = original_height * scale
                    
                    # Center the image
                    x = (page_width - new_width) / 2
                    y = (page_height - new_height) / 2
                    
                    # Save frame as temporary image
                    frame_path = os.path.join(self.temp_dir, f'frame_{frame_number}_{uuid.uuid4().hex}.jpg')
                    frame_copy.save(frame_path, 'JPEG', quality=quality)
                    
                    # Draw frame
                    c.drawImage(frame_path, x, y, width=new_width, height=new_height)
                    
                    # Add frame info
                    c.setFont("Helvetica", 10)
                    c.drawString(margin, margin, f"Frame {frame_number + 1}")
                    
                    c.showPage()
                    frame_number += 1
                    
            except Exception as e:
                print(f"Error processing GIF frames: {e}")
                return None
            
            c.save()
        
        return output_path
    
    def pdf_to_gif(self, pdf_file, duration=500, loop=0, quality=85, max_width=800):
        """Convert PDF pages to GIF animation"""
        pdf_bytes = pdf_file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        frames = []
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            
            # Calculate scale to limit width
            page_rect = page.rect
            scale = min(max_width / page_rect.width, 1.0)
            
            # Create matrix for scaling
            mat = fitz.Matrix(scale, scale)
            pix = page.get_pixmap(matrix=mat)
            
            # Convert to PIL Image
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            frames.append(img)
        
        pdf_doc.close()
        
        if not frames:
            return None
        
        # Create GIF
        output_path = os.path.join(self.temp_dir, f'pdf_to_gif_{uuid.uuid4().hex}.gif')
        
        # Optimize colors for better GIF compression
        optimized_frames = []
        for frame in frames:
            # Reduce colors to 256 for GIF format
            frame_optimized = frame.quantize(colors=256, method=Image.Quantize.MEDIANCUT)
            optimized_frames.append(frame_optimized)
        
        # Save as GIF
        optimized_frames[0].save(
            output_path,
            save_all=True,
            append_images=optimized_frames[1:],
            duration=duration,
            loop=loop,
            optimize=True
        )
        
        return output_path
    
    def extract_gif_frames(self, gif_file, output_format='PNG'):
        """Extract individual frames from GIF"""
        gif_data = gif_file.read()
        gif = Image.open(io.BytesIO(gif_data))
        
        frame_paths = []
        frame_number = 0
        
        try:
            for frame in ImageSequence.Iterator(gif):
                frame_copy = frame.copy()
                
                # Convert to RGB if saving as JPEG
                if output_format.upper() == 'JPEG' and frame_copy.mode != 'RGB':
                    frame_copy = frame_copy.convert('RGB')
                
                # Save frame
                frame_path = os.path.join(
                    self.temp_dir, 
                    f'frame_{frame_number:04d}_{uuid.uuid4().hex}.{output_format.lower()}'
                )
                
                if output_format.upper() == 'JPEG':
                    frame_copy.save(frame_path, 'JPEG', quality=95, optimize=True)
                else:
                    frame_copy.save(frame_path, output_format, optimize=True)
                
                frame_paths.append(frame_path)
                frame_number += 1
                
        except Exception as e:
            print(f"Error extracting GIF frames: {e}")
            return []
        
        return frame_paths
    
    def create_gif_from_images(self, image_files, duration=500, loop=0, optimize=True):
        """Create GIF animation from multiple images"""
        frames = []
        
        for image_file in image_files:
            img_data = image_file.read()
            img = Image.open(io.BytesIO(img_data))
            
            # Convert to RGB first, then to palette mode for GIF
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize if too large
            max_size = (800, 600)
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Convert to palette mode for better GIF compression
            img_palette = img.quantize(colors=256, method=Image.Quantize.MEDIANCUT)
            frames.append(img_palette)
        
        if not frames:
            return None
        
        # Create GIF
        output_path = os.path.join(self.temp_dir, f'created_gif_{uuid.uuid4().hex}.gif')
        
        frames[0].save(
            output_path,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=loop,
            optimize=optimize
        )
        
        return output_path
    
    def get_gif_info(self, gif_file):
        """Get information about GIF animation"""
        gif_data = gif_file.read()
        gif = Image.open(io.BytesIO(gif_data))
        
        info = {
            'width': gif.width,
            'height': gif.height,
            'mode': gif.mode,
            'format': gif.format,
            'is_animated': getattr(gif, 'is_animated', False),
            'frame_count': 0,
            'duration': [],
            'total_duration': 0
        }
        
        if info['is_animated']:
            frame_count = 0
            total_duration = 0
            
            try:
                for frame in ImageSequence.Iterator(gif):
                    frame_count += 1
                    duration = frame.info.get('duration', 100)
                    info['duration'].append(duration)
                    total_duration += duration
                
                info['frame_count'] = frame_count
                info['total_duration'] = total_duration
                info['average_duration'] = total_duration / frame_count if frame_count > 0 else 0
                
            except Exception as e:
                print(f"Error analyzing GIF: {e}")
        
        return info
    
    def resize_gif(self, gif_file, new_width, new_height, maintain_aspect=True):
        """Resize GIF animation"""
        gif_data = gif_file.read()
        gif = Image.open(io.BytesIO(gif_data))
        
        if maintain_aspect:
            # Calculate new dimensions maintaining aspect ratio
            original_width, original_height = gif.size
            aspect_ratio = original_width / original_height
            
            if new_width / new_height > aspect_ratio:
                new_width = int(new_height * aspect_ratio)
            else:
                new_height = int(new_width / aspect_ratio)
        
        frames = []
        durations = []
        
        try:
            for frame in ImageSequence.Iterator(gif):
                # Resize frame
                resized_frame = frame.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Convert to palette mode for GIF
                if resized_frame.mode != 'P':
                    resized_frame = resized_frame.quantize(colors=256)
                
                frames.append(resized_frame)
                durations.append(frame.info.get('duration', 100))
                
        except Exception as e:
            print(f"Error resizing GIF: {e}")
            return None
        
        if not frames:
            return None
        
        # Create resized GIF
        output_path = os.path.join(self.temp_dir, f'resized_gif_{uuid.uuid4().hex}.gif')
        
        frames[0].save(
            output_path,
            save_all=True,
            append_images=frames[1:],
            duration=durations,
            loop=gif.info.get('loop', 0),
            optimize=True
        )
        
        return output_path
