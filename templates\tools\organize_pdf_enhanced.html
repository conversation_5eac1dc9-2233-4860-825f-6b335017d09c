{% extends "base.html" %}

{% block title %}Organize PDF - {{ t.site_name }}{% endblock %}

{% block extra_head %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="{{ url_for('static', filename='js/pdf-viewer.js') }}"></script>
<style>
.page-thumbnail {
    transition: all 0.3s ease;
    cursor: grab;
}

.page-thumbnail:active {
    cursor: grabbing;
}

.page-thumbnail.selected {
    ring: 2px solid #3b82f6;
    ring-offset: 2px;
}

.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    transform: scale(1.05);
}

.pdf-viewer {
    height: 600px;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
}

.page-actions {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

.page-thumbnail:hover .page-actions {
    opacity: 1;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-sort text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Organize PDF</h1>
        <p class="text-xl text-gray-600">Reorder, rotate, duplicate, and manage PDF pages</p>
    </div>
    
    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Left Panel - Controls -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Upload Area -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Upload PDF</h2>
                <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-all duration-300">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-4">Drag & drop PDF or click to select</p>
                    <input type="file" id="fileInput" accept=".pdf" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-4 py-2 rounded-lg">
                        Select PDF File
                    </button>
                </div>
                
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                            <div>
                                <p id="fileName" class="font-medium text-gray-800"></p>
                                <p id="fileSize" class="text-sm text-gray-500"></p>
                                <p id="pageCount" class="text-sm text-blue-600"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Page Operations -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Page Operations</h2>
                <div class="space-y-3">
                    <button id="selectAllBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-check-square mr-2"></i>Select All
                    </button>
                    <button id="deselectAllBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-square mr-2"></i>Deselect All
                    </button>
                    <button id="duplicateBtn" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" disabled>
                        <i class="fas fa-copy mr-2"></i>Duplicate Selected
                    </button>
                    <button id="deleteBtn" class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors" disabled>
                        <i class="fas fa-trash mr-2"></i>Delete Selected
                    </button>
                </div>
            </div>
            
            <!-- Rotation Controls -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Rotation</h2>
                <div class="grid grid-cols-2 gap-3">
                    <button id="rotateLeftBtn" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors" disabled>
                        <i class="fas fa-undo mr-2"></i>90° Left
                    </button>
                    <button id="rotateRightBtn" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors" disabled>
                        <i class="fas fa-redo mr-2"></i>90° Right
                    </button>
                    <button id="rotate180Btn" class="col-span-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors" disabled>
                        <i class="fas fa-sync mr-2"></i>180°
                    </button>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
                <div class="space-y-3">
                    <button id="reverseOrderBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-exchange-alt mr-2"></i>Reverse Order
                    </button>
                    <button id="removeOddBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-filter mr-2"></i>Remove Odd Pages
                    </button>
                    <button id="removeEvenBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-filter mr-2"></i>Remove Even Pages
                    </button>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="space-y-3">
                    <button id="previewBtn" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-eye mr-2"></i>Preview Changes
                    </button>
                    <button id="applyBtn" class="w-full btn-primary text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-save mr-2"></i>Apply Changes
                    </button>
                    <button id="resetBtn" class="w-full px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors">
                        <i class="fas fa-undo mr-2"></i>Reset Changes
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Middle Panel - Page Thumbnails -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Page Thumbnails</h2>
                    <div class="text-sm text-blue-600">
                        <i class="fas fa-info-circle mr-1"></i>
                        Drag to reorder • Click to select • Right-click for options
                    </div>
                </div>
                
                <div id="thumbnailsContainer" class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4 min-h-96">
                    <div class="col-span-full flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-file-pdf text-6xl mb-4"></i>
                            <p>Upload a PDF file to start organizing</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Panel - PDF Viewer -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Page Preview</h2>
                <div id="pdfViewerContainer" class="relative">
                    <div id="pdfViewer" class="pdf-viewer">
                        <div class="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-eye text-4xl mb-4"></i>
                                <p class="text-sm">Click a page thumbnail to preview</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Context Menu -->
    <div id="contextMenu" class="fixed bg-white border border-gray-300 rounded-lg shadow-lg py-2 hidden z-50">
        <button class="context-menu-item w-full px-4 py-2 text-left hover:bg-gray-100" data-action="duplicate">
            <i class="fas fa-copy mr-2"></i>Duplicate Page
        </button>
        <button class="context-menu-item w-full px-4 py-2 text-left hover:bg-gray-100" data-action="delete">
            <i class="fas fa-trash mr-2"></i>Delete Page
        </button>
        <hr class="my-1">
        <button class="context-menu-item w-full px-4 py-2 text-left hover:bg-gray-100" data-action="rotate-left">
            <i class="fas fa-undo mr-2"></i>Rotate Left
        </button>
        <button class="context-menu-item w-full px-4 py-2 text-left hover:bg-gray-100" data-action="rotate-right">
            <i class="fas fa-redo mr-2"></i>Rotate Right
        </button>
    </div>
    
    <!-- Progress Modal -->
    <div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Processing</h3>
                <p class="text-gray-600">Organizing your PDF pages...</p>
            </div>
        </div>
    </div>
    
    <!-- Result Modal -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">PDF Organized!</h3>
                <p class="text-gray-600 mb-6">Your PDF has been successfully reorganized.</p>
                
                <div class="flex justify-center space-x-4">
                    <button id="downloadBtn" class="btn-primary text-white px-6 py-2 rounded-lg font-semibold">
                        <i class="fas fa-download mr-2"></i>Download
                    </button>
                    <button id="newOrganizeBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        Organize Another PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class OrganizeTool {
    constructor() {
        this.pdfViewer = null;
        this.selectedFile = null;
        this.pages = [];
        this.selectedPages = new Set();
        this.sortable = null;
        this.contextMenuTarget = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                this.handleFileSelect(files[0]);
            }
        });

        // Page operations
        document.getElementById('selectAllBtn').addEventListener('click', () => this.selectAllPages());
        document.getElementById('deselectAllBtn').addEventListener('click', () => this.deselectAllPages());
        document.getElementById('duplicateBtn').addEventListener('click', () => this.duplicateSelectedPages());
        document.getElementById('deleteBtn').addEventListener('click', () => this.deleteSelectedPages());

        // Rotation controls
        document.getElementById('rotateLeftBtn').addEventListener('click', () => this.rotateSelectedPages(-90));
        document.getElementById('rotateRightBtn').addEventListener('click', () => this.rotateSelectedPages(90));
        document.getElementById('rotate180Btn').addEventListener('click', () => this.rotateSelectedPages(180));

        // Quick actions
        document.getElementById('reverseOrderBtn').addEventListener('click', () => this.reversePageOrder());
        document.getElementById('removeOddBtn').addEventListener('click', () => this.removeOddPages());
        document.getElementById('removeEvenBtn').addEventListener('click', () => this.removeEvenPages());

        // Action buttons
        document.getElementById('previewBtn').addEventListener('click', () => this.previewChanges());
        document.getElementById('applyBtn').addEventListener('click', () => this.applyChanges());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetChanges());
        document.getElementById('newOrganizeBtn').addEventListener('click', () => this.resetTool());

        // Context menu
        document.addEventListener('click', () => this.hideContextMenu());
        document.querySelectorAll('.context-menu-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleContextMenuAction(e.target.dataset.action));
        });
    }

    async handleFileSelect(file) {
        if (!file || file.type !== 'application/pdf') {
            alert('Please select a valid PDF file.');
            return;
        }

        this.selectedFile = file;

        // Update file info
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = (file.size / 1024 / 1024).toFixed(2) + ' MB';
        document.getElementById('fileInfo').classList.remove('hidden');

        // Initialize PDF viewer and load thumbnails
        await this.initializePDFViewer(file);
        await this.loadThumbnails(file);
    }

    async initializePDFViewer(file) {
        try {
            // Destroy existing viewer if any
            if (this.pdfViewer) {
                this.pdfViewer.destroy();
            }

            // Create new PDF viewer
            this.pdfViewer = new PDFViewer('pdfViewer', {
                showThumbnails: false,
                allowSelection: false
            });

            // Load PDF
            await this.pdfViewer.loadPDF(file);

        } catch (error) {
            console.error('Error initializing PDF viewer:', error);
            alert('Error loading PDF: ' + error.message);
        }
    }

    async loadThumbnails(file) {
        try {
            const arrayBuffer = await file.arrayBuffer();
            const pdfDoc = await pdfjsLib.getDocument(arrayBuffer).promise;
            const totalPages = pdfDoc.numPages;

            // Update page count
            document.getElementById('pageCount').textContent = `${totalPages} pages`;

            // Clear existing thumbnails
            const container = document.getElementById('thumbnailsContainer');
            container.innerHTML = '';

            // Initialize pages array
            this.pages = [];

            // Generate thumbnails
            for (let i = 1; i <= totalPages; i++) {
                const page = await pdfDoc.getPage(i);
                const viewport = page.getViewport({ scale: 0.3 });

                const canvas = document.createElement('canvas');
                canvas.width = viewport.width;
                canvas.height = viewport.height;

                const ctx = canvas.getContext('2d');
                await page.render({ canvasContext: ctx, viewport }).promise;

                // Create thumbnail element
                const thumbnail = this.createThumbnailElement(i, canvas.toDataURL(), 0);
                container.appendChild(thumbnail);

                // Store page data
                this.pages.push({
                    originalIndex: i,
                    currentIndex: i,
                    rotation: 0,
                    canvas: canvas.toDataURL(),
                    element: thumbnail
                });
            }

            // Initialize sortable
            this.initializeSortable();

        } catch (error) {
            console.error('Error loading thumbnails:', error);
            alert('Error loading PDF thumbnails: ' + error.message);
        }
    }

    createThumbnailElement(pageNum, canvasData, rotation) {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'page-thumbnail relative bg-white border-2 border-gray-200 rounded-lg p-2 hover:border-blue-300 transition-colors';
        thumbnail.dataset.pageIndex = pageNum - 1;

        thumbnail.innerHTML = `
            <div class="relative">
                <img src="${canvasData}" class="w-full h-auto rounded" style="transform: rotate(${rotation}deg)">
                <div class="page-actions">
                    <button class="duplicate-page-btn w-6 h-6 bg-blue-600 text-white rounded-full text-xs hover:bg-blue-700" title="Duplicate">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="delete-page-btn w-6 h-6 bg-red-600 text-white rounded-full text-xs hover:bg-red-700 ml-1" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    ${pageNum}
                </div>
            </div>
        `;

        // Add event listeners
        thumbnail.addEventListener('click', (e) => this.handleThumbnailClick(e, pageNum - 1));
        thumbnail.addEventListener('contextmenu', (e) => this.handleContextMenu(e, pageNum - 1));

        // Page action buttons
        thumbnail.querySelector('.duplicate-page-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.duplicatePage(pageNum - 1);
        });

        thumbnail.querySelector('.delete-page-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.deletePage(pageNum - 1);
        });

        return thumbnail;
    }

    // Additional methods will be added in the next part...

    resetTool() {
        // Reset file selection
        this.selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');

        // Clear thumbnails
        document.getElementById('thumbnailsContainer').innerHTML = `
            <div class="col-span-full flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-pdf text-6xl mb-4"></i>
                    <p>Upload a PDF file to start organizing</p>
                </div>
            </div>
        `;

        // Reset PDF viewer
        if (this.pdfViewer) {
            this.pdfViewer.destroy();
            this.pdfViewer = null;
        }

        // Reset viewer container
        document.getElementById('pdfViewer').innerHTML = `
            <div class="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-eye text-4xl mb-4"></i>
                    <p class="text-sm">Click a page thumbnail to preview</p>
                </div>
            </div>
        `;

        // Reset state
        this.pages = [];
        this.selectedPages.clear();
        if (this.sortable) {
            this.sortable.destroy();
            this.sortable = null;
        }

        // Hide modals
        document.getElementById('resultModal').classList.add('hidden');
        document.getElementById('progressModal').classList.add('hidden');
    }
}

// Initialize the organize tool when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new OrganizeTool();
});
</script>
{% endblock %}
