{% extends "base.html" %}

{% block title %}Add Watermark - {{ t.site_name }}{% endblock %}

{% block extra_head %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script src="{{ url_for('static', filename='js/pdf-viewer.js') }}"></script>
<style>
.watermark-preview {
    position: absolute;
    pointer-events: none;
    border: 2px dashed #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    z-index: 10;
}

.watermark-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border: 1px solid white;
    cursor: nw-resize;
}

.pdf-viewer {
    height: 600px;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-tint text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Add Watermark</h1>
        <p class="text-xl text-gray-600">Add text, image, or stamp watermarks with interactive placement</p>
    </div>
    
    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Panel - Controls -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Upload Area -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Upload PDF</h2>
                <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-all duration-300">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-4">Drag & drop PDF or click to select</p>
                    <input type="file" id="fileInput" accept=".pdf" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-4 py-2 rounded-lg">
                        Select PDF File
                    </button>
                </div>
                
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                            <div>
                                <p id="fileName" class="font-medium text-gray-800"></p>
                                <p id="fileSize" class="text-sm text-gray-500"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Watermark Type -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Watermark Type</h2>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="radio" name="watermarkType" value="text" checked class="mr-2">
                        <i class="fas fa-font mr-2 text-blue-500"></i>
                        Text Watermark
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="watermarkType" value="image" class="mr-2">
                        <i class="fas fa-image mr-2 text-green-500"></i>
                        Image Watermark
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="watermarkType" value="stamp" class="mr-2">
                        <i class="fas fa-stamp mr-2 text-purple-500"></i>
                        Stamp Watermark
                    </label>
                </div>
            </div>
            
            <!-- Text Watermark Settings -->
            <div id="textSettings" class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Text Settings</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Text</label>
                        <input type="text" id="watermarkText" value="CONFIDENTIAL" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                            <input type="range" id="fontSize" min="12" max="72" value="36" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="fontSizeValue">36</span>pt</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Opacity</label>
                            <input type="range" id="textOpacity" min="0.1" max="1" step="0.1" value="0.5" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="textOpacityValue">50</span>%</div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                        <div class="flex space-x-2">
                            <input type="color" id="textColor" value="#ff0000" class="w-12 h-8 border border-gray-300 rounded">
                            <select id="fontFamily" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg">
                                <option value="Helvetica">Helvetica</option>
                                <option value="Times-Roman">Times New Roman</option>
                                <option value="Courier">Courier</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Rotation</label>
                        <input type="range" id="textRotation" min="-90" max="90" value="45" class="w-full">
                        <div class="text-center text-sm text-gray-600"><span id="textRotationValue">45</span>°</div>
                    </div>
                </div>
            </div>
            
            <!-- Image Watermark Settings -->
            <div id="imageSettings" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Image Settings</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
                        <input type="file" id="imageInput" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Scale</label>
                            <input type="range" id="imageScale" min="0.1" max="2" step="0.1" value="0.5" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="imageScaleValue">50</span>%</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Opacity</label>
                            <input type="range" id="imageOpacity" min="0.1" max="1" step="0.1" value="0.7" class="w-full">
                            <div class="text-center text-sm text-gray-600"><span id="imageOpacityValue">70</span>%</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Stamp Settings -->
            <div id="stampSettings" class="bg-white rounded-xl shadow-lg p-6 hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Stamp Settings</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Stamp</label>
                        <div id="stampGrid" class="grid grid-cols-2 gap-2">
                            <!-- Stamps will be loaded here -->
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                        <input type="range" id="stampSize" min="50" max="200" value="100" class="w-full">
                        <div class="text-center text-sm text-gray-600"><span id="stampSizeValue">100</span>px</div>
                    </div>
                </div>
            </div>
            
            <!-- Position Settings -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Position</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Placement</label>
                        <div class="grid grid-cols-3 gap-2 text-sm">
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="top-left">Top Left</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="top-center">Top Center</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="top-right">Top Right</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="center-left">Center Left</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50 bg-blue-50 border-blue-300" data-position="center">Center</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="center-right">Center Right</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="bottom-left">Bottom Left</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="bottom-center">Bottom Center</button>
                            <button class="position-btn p-2 border rounded hover:bg-gray-50" data-position="bottom-right">Bottom Right</button>
                        </div>
                    </div>
                    
                    <div class="text-sm text-blue-600">
                        <i class="fas fa-info-circle mr-1"></i>
                        Click on the PDF preview to place watermark manually
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="space-y-3">
                    <button id="previewWatermark" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-eye mr-2"></i>Preview Watermark
                    </button>
                    <button id="applyWatermark" class="w-full btn-primary text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-tint mr-2"></i>Apply Watermark
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Right Panel - PDF Viewer -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">PDF Preview</h2>
                <div id="pdfViewerContainer" class="relative">
                    <div id="pdfViewer" class="pdf-viewer">
                        <div class="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-file-pdf text-6xl mb-4"></i>
                                <p>Upload a PDF file to start adding watermarks</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Progress Modal -->
    <div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Processing</h3>
                <p class="text-gray-600">Adding watermark to your PDF...</p>
            </div>
        </div>
    </div>
    
    <!-- Result Modal -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Watermark Applied!</h3>
                <p class="text-gray-600 mb-6">Your PDF has been successfully watermarked.</p>
                
                <div class="flex justify-center space-x-4">
                    <button id="downloadBtn" class="btn-primary text-white px-6 py-2 rounded-lg font-semibold">
                        <i class="fas fa-download mr-2"></i>Download
                    </button>
                    <button id="newWatermarkBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        New Watermark
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class WatermarkTool {
    constructor() {
        this.pdfViewer = null;
        this.selectedFile = null;
        this.watermarkPreviews = [];
        this.currentWatermarkType = 'text';
        this.selectedPosition = 'center';
        this.stamps = [
            'APPROVED', 'REJECTED', 'CONFIDENTIAL', 'DRAFT', 'COPY', 'ORIGINAL',
            'URGENT', 'PAID', 'VOID', 'SAMPLE', 'CLASSIFIED', 'TOP SECRET'
        ];

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStamps();
        this.updateSliderValues();
    }

    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                this.handleFileSelect(files[0]);
            }
        });

        // Watermark type selection
        document.querySelectorAll('input[name="watermarkType"]').forEach(radio => {
            radio.addEventListener('change', (e) => this.switchWatermarkType(e.target.value));
        });

        // Text settings
        document.getElementById('watermarkText').addEventListener('input', () => this.updatePreview());
        document.getElementById('fontSize').addEventListener('input', (e) => {
            document.getElementById('fontSizeValue').textContent = e.target.value;
            this.updatePreview();
        });
        document.getElementById('textOpacity').addEventListener('input', (e) => {
            document.getElementById('textOpacityValue').textContent = Math.round(e.target.value * 100);
            this.updatePreview();
        });
        document.getElementById('textColor').addEventListener('change', () => this.updatePreview());
        document.getElementById('fontFamily').addEventListener('change', () => this.updatePreview());
        document.getElementById('textRotation').addEventListener('input', (e) => {
            document.getElementById('textRotationValue').textContent = e.target.value;
            this.updatePreview();
        });

        // Image settings
        document.getElementById('imageInput').addEventListener('change', (e) => this.handleImageSelect(e.target.files[0]));
        document.getElementById('imageScale').addEventListener('input', (e) => {
            document.getElementById('imageScaleValue').textContent = Math.round(e.target.value * 100);
            this.updatePreview();
        });
        document.getElementById('imageOpacity').addEventListener('input', (e) => {
            document.getElementById('imageOpacityValue').textContent = Math.round(e.target.value * 100);
            this.updatePreview();
        });

        // Stamp settings
        document.getElementById('stampSize').addEventListener('input', (e) => {
            document.getElementById('stampSizeValue').textContent = e.target.value;
            this.updatePreview();
        });

        // Position buttons
        document.querySelectorAll('.position-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectPosition(e.target.dataset.position));
        });

        // Action buttons
        document.getElementById('previewWatermark').addEventListener('click', () => this.previewWatermark());
        document.getElementById('applyWatermark').addEventListener('click', () => this.applyWatermark());
        document.getElementById('newWatermarkBtn').addEventListener('click', () => this.resetTool());
    }

    updateSliderValues() {
        document.getElementById('fontSizeValue').textContent = document.getElementById('fontSize').value;
        document.getElementById('textOpacityValue').textContent = Math.round(document.getElementById('textOpacity').value * 100);
        document.getElementById('textRotationValue').textContent = document.getElementById('textRotation').value;
        document.getElementById('imageScaleValue').textContent = Math.round(document.getElementById('imageScale').value * 100);
        document.getElementById('imageOpacityValue').textContent = Math.round(document.getElementById('imageOpacity').value * 100);
        document.getElementById('stampSizeValue').textContent = document.getElementById('stampSize').value;
    }

    async handleFileSelect(file) {
        if (!file || file.type !== 'application/pdf') {
            alert('Please select a valid PDF file.');
            return;
        }

        this.selectedFile = file;

        // Update file info
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = (file.size / 1024 / 1024).toFixed(2) + ' MB';
        document.getElementById('fileInfo').classList.remove('hidden');

        // Initialize PDF viewer
        await this.initializePDFViewer(file);
    }

    async initializePDFViewer(file) {
        try {
            // Destroy existing viewer if any
            if (this.pdfViewer) {
                this.pdfViewer.destroy();
            }

            // Create new PDF viewer with interactive features
            this.pdfViewer = new PDFViewer('pdfViewer', {
                showThumbnails: true,
                allowSelection: true,
                allowAnnotation: true
            });

            // Load PDF
            await this.pdfViewer.loadPDF(file);

            // Add click handler for watermark placement
            this.setupWatermarkPlacement();

        } catch (error) {
            console.error('Error initializing PDF viewer:', error);
            alert('Error loading PDF: ' + error.message);
        }
    }

    setupWatermarkPlacement() {
        const canvas = document.getElementById('pdfCanvas');
        if (!canvas) return;

        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;

            this.placeWatermarkAt(x, y);
        });
    }

    placeWatermarkAt(x, y) {
        // Clear existing previews
        this.clearWatermarkPreviews();

        // Create watermark preview at clicked position
        const preview = this.createWatermarkPreview(x, y);
        if (preview) {
            this.watermarkPreviews.push({
                x, y,
                element: preview,
                page: this.pdfViewer.getCurrentPage()
            });
        }
    }

    createWatermarkPreview(x, y) {
        const canvas = document.getElementById('pdfCanvas');
        if (!canvas) return null;

        const container = canvas.parentElement;
        const preview = document.createElement('div');
        preview.className = 'watermark-preview';

        // Position the preview
        const rect = canvas.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        const left = (rect.left - containerRect.left) + (x * rect.width);
        const top = (rect.top - containerRect.top) + (y * rect.height);

        preview.style.left = left + 'px';
        preview.style.top = top + 'px';

        // Set content based on watermark type
        this.updatePreviewContent(preview);

        container.appendChild(preview);
        return preview;
    }

    // Additional methods will be added in the next part...

    loadStamps() {
        const stampGrid = document.getElementById('stampGrid');
        stampGrid.innerHTML = '';

        this.stamps.forEach(stamp => {
            const stampItem = document.createElement('div');
            stampItem.className = 'stamp-item p-2 border border-gray-300 rounded text-center cursor-pointer hover:bg-gray-50 text-xs';
            stampItem.dataset.stamp = stamp;
            stampItem.textContent = stamp;

            stampItem.addEventListener('click', () => {
                document.querySelectorAll('.stamp-item').forEach(item => {
                    item.classList.remove('selected', 'bg-blue-100', 'border-blue-500');
                });
                stampItem.classList.add('selected', 'bg-blue-100', 'border-blue-500');
                this.updatePreview();
            });

            stampGrid.appendChild(stampItem);
        });

        // Select first stamp by default
        if (stampGrid.firstChild) {
            stampGrid.firstChild.click();
        }
    }

    switchWatermarkType(type) {
        this.currentWatermarkType = type;

        // Hide all settings panels
        document.getElementById('textSettings').classList.add('hidden');
        document.getElementById('imageSettings').classList.add('hidden');
        document.getElementById('stampSettings').classList.add('hidden');

        // Show relevant settings panel
        document.getElementById(type + 'Settings').classList.remove('hidden');

        // Update preview
        this.updatePreview();
    }

    updatePreview() {
        // Update all existing watermark previews
        this.watermarkPreviews.forEach(preview => {
            this.updatePreviewContent(preview.element);
        });
    }

    updatePreviewContent(preview) {
        switch (this.currentWatermarkType) {
            case 'text':
                this.updateTextPreview(preview);
                break;
            case 'image':
                this.updateImagePreview(preview);
                break;
            case 'stamp':
                this.updateStampPreview(preview);
                break;
        }
    }

    updateTextPreview(preview) {
        const text = document.getElementById('watermarkText').value;
        const fontSize = document.getElementById('fontSize').value;
        const opacity = document.getElementById('textOpacity').value;
        const color = document.getElementById('textColor').value;
        const rotation = document.getElementById('textRotation').value;
        const fontFamily = document.getElementById('fontFamily').value;

        preview.innerHTML = text;
        preview.style.fontSize = fontSize + 'px';
        preview.style.color = color;
        preview.style.opacity = opacity;
        preview.style.fontFamily = fontFamily;
        preview.style.transform = `rotate(${rotation}deg)`;
        preview.style.whiteSpace = 'nowrap';
        preview.style.padding = '4px 8px';
    }

    previewWatermark() {
        if (!this.selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }

        if (this.watermarkPreviews.length === 0) {
            // Auto-place watermark at selected position
            const coords = this.getPositionCoordinates(this.selectedPosition);
            this.placeWatermarkAt(coords.x, coords.y);
        }

        alert('Watermark preview is shown on the PDF. Click "Apply Watermark" to save the changes.');
    }

    getPositionCoordinates(position) {
        const positions = {
            'top-left': { x: 0.1, y: 0.1 },
            'top-center': { x: 0.5, y: 0.1 },
            'top-right': { x: 0.9, y: 0.1 },
            'center-left': { x: 0.1, y: 0.5 },
            'center': { x: 0.5, y: 0.5 },
            'center-right': { x: 0.9, y: 0.5 },
            'bottom-left': { x: 0.1, y: 0.9 },
            'bottom-center': { x: 0.5, y: 0.9 },
            'bottom-right': { x: 0.9, y: 0.9 }
        };

        return positions[position] || positions['center'];
    }

    clearWatermarkPreviews() {
        this.watermarkPreviews.forEach(preview => {
            if (preview.element && preview.element.parentNode) {
                preview.element.parentNode.removeChild(preview.element);
            }
        });
        this.watermarkPreviews = [];
    }

    async applyWatermark() {
        if (!this.selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }

        if (this.watermarkPreviews.length === 0) {
            alert('Please place a watermark first by clicking on the PDF or selecting a position.');
            return;
        }

        try {
            this.showProgress(true);

            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('watermark_type', this.currentWatermarkType);

            // Add watermark positions
            formData.append('watermarks', JSON.stringify(this.watermarkPreviews.map(w => ({
                x: w.x,
                y: w.y,
                page: w.page
            }))));

            // Add settings based on type
            if (this.currentWatermarkType === 'text') {
                formData.append('text', document.getElementById('watermarkText').value);
                formData.append('font_size', document.getElementById('fontSize').value);
                formData.append('opacity', document.getElementById('textOpacity').value);
                formData.append('color', document.getElementById('textColor').value);
                formData.append('rotation', document.getElementById('textRotation').value);
                formData.append('font_family', document.getElementById('fontFamily').value);
            } else if (this.currentWatermarkType === 'stamp') {
                const selectedStamp = document.querySelector('.stamp-item.selected');
                if (selectedStamp) {
                    formData.append('stamp_text', selectedStamp.dataset.stamp);
                }
                formData.append('stamp_size', document.getElementById('stampSize').value);
            }

            const response = await fetch('/api/add_watermark', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const blob = await response.blob();
                this.showProgress(false);
                this.showResult(blob);
            } else {
                throw new Error('Watermark application failed');
            }

        } catch (error) {
            this.showProgress(false);
            alert('Error applying watermark: ' + error.message);
        }
    }

    showProgress(show) {
        document.getElementById('progressModal').classList.toggle('hidden', !show);
    }

    showResult(blob) {
        document.getElementById('resultModal').classList.remove('hidden');

        // Setup download
        const url = window.URL.createObjectURL(blob);
        document.getElementById('downloadBtn').onclick = () => {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'watermarked.pdf';
            a.click();
        };
    }

    resetTool() {
        // Reset file selection
        this.selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');

        // Reset watermark previews
        this.clearWatermarkPreviews();

        // Reset PDF viewer
        if (this.pdfViewer) {
            this.pdfViewer.destroy();
            this.pdfViewer = null;
        }

        // Reset viewer container
        document.getElementById('pdfViewer').innerHTML = `
            <div class="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-pdf text-6xl mb-4"></i>
                    <p>Upload a PDF file to start adding watermarks</p>
                </div>
            </div>
        `;

        // Hide modals
        document.getElementById('resultModal').classList.add('hidden');
        document.getElementById('progressModal').classList.add('hidden');
    }
}

// Initialize the watermark tool when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new WatermarkTool();
});
</script>
{% endblock %}
