import fitz  # PyMuPDF
from PIL import Image, ImageDraw, ImageFont
import io
import os
import tempfile
import uuid
import qrcode
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.utils import ImageReader
import base64

class ImageProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def pdf_to_images(self, file, format='PNG', quality=95, dpi=150):
        """Convert PDF pages to images"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        image_paths = []
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            
            # Create matrix for higher resolution
            mat = fitz.Matrix(dpi/72, dpi/72)
            pix = page.get_pixmap(matrix=mat)
            
            # Convert to PIL Image
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            
            # Save image
            output_path = os.path.join(self.temp_dir, f'page_{page_num + 1}_{uuid.uuid4().hex}.{format.lower()}')
            
            if format.upper() == 'JPEG':
                # Convert RGBA to RGB for JPEG
                if img.mode == 'RGBA':
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background
                img.save(output_path, format=format, quality=quality, optimize=True)
            else:
                img.save(output_path, format=format, optimize=True)
            
            image_paths.append(output_path)
        
        pdf_doc.close()
        return image_paths
    
    def images_to_pdf(self, files, page_size='A4', orientation='portrait'):
        """Convert images to PDF"""
        from reportlab.lib.pagesizes import A4, letter, landscape
        
        # Set page size
        if page_size == 'A4':
            page_size = A4
        else:
            page_size = letter
            
        if orientation == 'landscape':
            page_size = landscape(page_size)
        
        output_path = os.path.join(self.temp_dir, f'images_to_pdf_{uuid.uuid4().hex}.pdf')
        c = canvas.Canvas(output_path, pagesize=page_size)
        
        page_width, page_height = page_size
        
        for file in files:
            if file and file.filename:
                try:
                    # Read image
                    img_data = file.read()
                    img = Image.open(io.BytesIO(img_data))
                    
                    # Calculate dimensions to fit page while maintaining aspect ratio
                    img_width, img_height = img.size
                    
                    # Add margins
                    margin = 50
                    max_width = page_width - 2 * margin
                    max_height = page_height - 2 * margin
                    
                    # Calculate scaling factor
                    scale_x = max_width / img_width
                    scale_y = max_height / img_height
                    scale = min(scale_x, scale_y)
                    
                    new_width = img_width * scale
                    new_height = img_height * scale
                    
                    # Center the image
                    x = (page_width - new_width) / 2
                    y = (page_height - new_height) / 2
                    
                    # Create ImageReader object
                    img_reader = ImageReader(io.BytesIO(img_data))
                    
                    # Draw image on canvas
                    c.drawImage(img_reader, x, y, width=new_width, height=new_height)
                    c.showPage()
                    
                except Exception as e:
                    print(f"Error processing image {file.filename}: {str(e)}")
                    continue
        
        c.save()
        return output_path
    
    def extract_images_from_pdf(self, file):
        """Extract all images from PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        image_paths = []
        image_count = 0
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(pdf_doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        image_count += 1
                        output_path = os.path.join(self.temp_dir, f'extracted_image_{image_count}_{uuid.uuid4().hex}.png')
                        pix.save(output_path)
                        image_paths.append(output_path)
                    
                    pix = None
                except Exception as e:
                    print(f"Error extracting image: {str(e)}")
                    continue
        
        pdf_doc.close()
        return image_paths
    
    def create_qr_code(self, text, size=10, border=4):
        """Generate QR code from text"""
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=size,
            border=border,
        )
        qr.add_data(text)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        output_path = os.path.join(self.temp_dir, f'qr_code_{uuid.uuid4().hex}.png')
        img.save(output_path)
        
        return output_path
    
    def image_to_qr_pdf(self, text, title="QR Code"):
        """Create PDF with QR code"""
        qr_image_path = self.create_qr_code(text)
        
        output_path = os.path.join(self.temp_dir, f'qr_pdf_{uuid.uuid4().hex}.pdf')
        c = canvas.Canvas(output_path, pagesize=A4)
        
        page_width, page_height = A4
        
        # Add title
        c.setFont("Helvetica-Bold", 24)
        c.drawCentredText(page_width/2, page_height - 100, title)
        
        # Add QR code
        qr_size = 300
        x = (page_width - qr_size) / 2
        y = (page_height - qr_size) / 2
        
        c.drawImage(qr_image_path, x, y, width=qr_size, height=qr_size)
        
        # Add text below QR code
        c.setFont("Helvetica", 12)
        text_lines = self.wrap_text(text, 60)
        y_text = y - 50
        
        for line in text_lines:
            c.drawCentredText(page_width/2, y_text, line)
            y_text -= 20
        
        c.save()
        return output_path
    
    def wrap_text(self, text, width):
        """Wrap text to specified width"""
        words = text.split()
        lines = []
        current_line = []
        
        for word in words:
            if len(' '.join(current_line + [word])) <= width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
    
    def resize_image(self, file, width=None, height=None, maintain_aspect=True):
        """Resize image with optional aspect ratio maintenance"""
        img_data = file.read()
        img = Image.open(io.BytesIO(img_data))
        
        original_width, original_height = img.size
        
        if maintain_aspect:
            if width and not height:
                height = int((width / original_width) * original_height)
            elif height and not width:
                width = int((height / original_height) * original_width)
            elif width and height:
                # Use the smaller scaling factor to maintain aspect ratio
                scale_x = width / original_width
                scale_y = height / original_height
                scale = min(scale_x, scale_y)
                width = int(original_width * scale)
                height = int(original_height * scale)
        
        resized_img = img.resize((width, height), Image.Resampling.LANCZOS)
        
        output_path = os.path.join(self.temp_dir, f'resized_{uuid.uuid4().hex}.png')
        resized_img.save(output_path, optimize=True)
        
        return output_path
    
    def convert_image_format(self, file, target_format='PNG', quality=95):
        """Convert image to different format"""
        img_data = file.read()
        img = Image.open(io.BytesIO(img_data))
        
        # Handle transparency for JPEG
        if target_format.upper() == 'JPEG' and img.mode in ('RGBA', 'LA'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            if img.mode == 'RGBA':
                background.paste(img, mask=img.split()[-1])
            else:
                background.paste(img, mask=img.split()[-1])
            img = background
        
        output_path = os.path.join(self.temp_dir, f'converted_{uuid.uuid4().hex}.{target_format.lower()}')
        
        if target_format.upper() == 'JPEG':
            img.save(output_path, format=target_format, quality=quality, optimize=True)
        else:
            img.save(output_path, format=target_format, optimize=True)
        
        return output_path
    
    def create_zip(self, file_paths, zip_name):
        """Create a zip file containing multiple files"""
        import zipfile
        
        zip_path = os.path.join(self.temp_dir, zip_name)
        
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for i, file_path in enumerate(file_paths):
                filename = f"image_{i+1}.{file_path.split('.')[-1]}"
                zipf.write(file_path, filename)
        
        return zip_path
