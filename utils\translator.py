import fitz  # PyMuPDF
import requests
import json
import os
import tempfile
import uuid
from urllib.parse import quote
import re

class PDFTranslator:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def extract_text_from_pdf(self, file):
        """Extract text from PDF with page information"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        extracted_text = []
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            text = page.get_text()
            
            if text.strip():  # Only include pages with text
                extracted_text.append({
                    'page': page_num + 1,
                    'text': text.strip()
                })
        
        pdf_doc.close()
        return extracted_text
    
    def detect_language(self, text):
        """Simple language detection based on character patterns"""
        # Count Arabic characters
        arabic_chars = len(re.findall(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', text))
        # Count English characters
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        total_chars = arabic_chars + english_chars
        
        if total_chars == 0:
            return 'unknown'
        
        arabic_ratio = arabic_chars / total_chars
        
        if arabic_ratio > 0.3:
            return 'ar'
        else:
            return 'en'
    
    def create_google_translate_url(self, text, source_lang='auto', target_lang='en'):
        """Create Google Translate URL for the text"""
        # Encode text for URL
        encoded_text = quote(text[:5000])  # Limit text length for URL
        
        # Create Google Translate URL
        base_url = "https://translate.google.com/"
        url = f"{base_url}?sl={source_lang}&tl={target_lang}&text={encoded_text}"
        
        return url
    
    def prepare_translation_data(self, file, target_lang='en'):
        """Prepare data for translation interface"""
        extracted_pages = self.extract_text_from_pdf(file)
        
        translation_data = {
            'pages': [],
            'total_pages': len(extracted_pages),
            'target_language': target_lang
        }
        
        for page_data in extracted_pages:
            text = page_data['text']
            detected_lang = self.detect_language(text)
            
            # Create translation URL
            if detected_lang == 'ar' and target_lang == 'en':
                translate_url = self.create_google_translate_url(text, 'ar', 'en')
            elif detected_lang == 'en' and target_lang == 'ar':
                translate_url = self.create_google_translate_url(text, 'en', 'ar')
            else:
                translate_url = self.create_google_translate_url(text, 'auto', target_lang)
            
            # Split text into chunks for better display
            text_chunks = self.split_text_into_chunks(text, 1000)
            
            page_info = {
                'page_number': page_data['page'],
                'original_text': text,
                'detected_language': detected_lang,
                'text_chunks': text_chunks,
                'translate_url': translate_url,
                'word_count': len(text.split()),
                'char_count': len(text)
            }
            
            translation_data['pages'].append(page_info)
        
        return translation_data
    
    def split_text_into_chunks(self, text, chunk_size=1000):
        """Split text into smaller chunks for translation"""
        words = text.split()
        chunks = []
        current_chunk = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1  # +1 for space
            
            if current_length + word_length > chunk_size and current_chunk:
                chunks.append(' '.join(current_chunk))
                current_chunk = [word]
                current_length = word_length
            else:
                current_chunk.append(word)
                current_length += word_length
        
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        return chunks
    
    def create_translation_html(self, translation_data, output_filename=None):
        """Create HTML file with translation interface"""
        if not output_filename:
            output_filename = f'translation_{uuid.uuid4().hex}.html'
        
        output_path = os.path.join(self.temp_dir, output_filename)
        
        html_content = self._generate_translation_html(translation_data)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def _generate_translation_html(self, data):
        """Generate HTML content for translation interface"""
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Translation - pdf2any</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .arabic {{ font-family: 'Arial', sans-serif; direction: rtl; text-align: right; }}
        .english {{ font-family: 'Arial', sans-serif; direction: ltr; text-align: left; }}
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-language text-blue-600 mr-3"></i>
                PDF Translation
            </h1>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-blue-600">{data['total_pages']}</div>
                    <div class="text-gray-600">Pages</div>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-green-600">{sum(page['word_count'] for page in data['pages'])}</div>
                    <div class="text-gray-600">Words</div>
                </div>
                <div class="bg-purple-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-purple-600">{data['target_language'].upper()}</div>
                    <div class="text-gray-600">Target Language</div>
                </div>
            </div>
        </div>
        
        <div class="space-y-6">
"""
        
        for page in data['pages']:
            lang_class = 'arabic' if page['detected_language'] == 'ar' else 'english'
            
            html += f"""
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">
                        Page {page['page_number']}
                        <span class="text-sm text-gray-500 ml-2">
                            ({page['detected_language'].upper()}) - {page['word_count']} words
                        </span>
                    </h2>
                    <a href="{page['translate_url']}" target="_blank" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Translate with Google
                    </a>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-4 {lang_class}">
                    <h3 class="font-medium text-gray-700 mb-2">Original Text:</h3>
                    <div class="text-gray-800 leading-relaxed whitespace-pre-wrap">{page['original_text']}</div>
                </div>
                
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
"""
            
            for i, chunk in enumerate(page['text_chunks']):
                chunk_url = self.create_google_translate_url(chunk, page['detected_language'], data['target_language'])
                html += f"""
                    <div class="border border-gray-200 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-600">Chunk {i+1}</span>
                            <a href="{chunk_url}" target="_blank" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-language mr-1"></i>Translate
                            </a>
                        </div>
                        <div class="text-sm text-gray-700 {lang_class}">{chunk[:200]}{'...' if len(chunk) > 200 else ''}</div>
                    </div>
"""
            
            html += """
                </div>
            </div>
"""
        
        html += """
        </div>
        
        <div class="bg-blue-50 rounded-lg p-6 mt-8">
            <h3 class="text-lg font-semibold text-blue-800 mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                How to use:
            </h3>
            <ol class="list-decimal list-inside space-y-2 text-blue-700">
                <li>Click "Translate with Google" for full page translation</li>
                <li>Use chunk translation links for smaller text portions</li>
                <li>Copy translated text from Google Translate</li>
                <li>Create a new document with the translated content</li>
            </ol>
        </div>
    </div>
</body>
</html>
"""
        
        return html
    
    def get_translation_summary(self, translation_data):
        """Get summary of translation data"""
        total_words = sum(page['word_count'] for page in translation_data['pages'])
        total_chars = sum(page['char_count'] for page in translation_data['pages'])
        
        languages = set(page['detected_language'] for page in translation_data['pages'])
        
        return {
            'total_pages': translation_data['total_pages'],
            'total_words': total_words,
            'total_characters': total_chars,
            'detected_languages': list(languages),
            'target_language': translation_data['target_language']
        }
