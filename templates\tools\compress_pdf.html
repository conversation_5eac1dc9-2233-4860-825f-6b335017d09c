{% extends "base.html" %}

{% block title %}Compress PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-compress-alt text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Compress PDF</h1>
        <p class="text-xl text-gray-600">Reduce PDF file size while maintaining quality</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to compress</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Compression Options -->
    <div id="compressionOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Compression Settings</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Low Compression -->
            <div class="compression-option border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-purple-300 transition-colors" data-level="low">
                <div class="text-center">
                    <i class="fas fa-feather text-3xl text-green-500 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Low Compression</h3>
                    <p class="text-sm text-gray-600 mb-3">Best quality, larger file size</p>
                    <div class="text-xs text-gray-500">
                        <p>• Minimal quality loss</p>
                        <p>• 10-30% size reduction</p>
                        <p>• Recommended for printing</p>
                    </div>
                </div>
            </div>
            
            <!-- Medium Compression -->
            <div class="compression-option border-2 border-purple-500 bg-purple-50 rounded-lg p-6 cursor-pointer" data-level="medium">
                <div class="text-center">
                    <i class="fas fa-balance-scale text-3xl text-purple-500 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Medium Compression</h3>
                    <p class="text-sm text-gray-600 mb-3">Balanced quality and size</p>
                    <div class="text-xs text-gray-500">
                        <p>• Good quality</p>
                        <p>• 30-50% size reduction</p>
                        <p>• Recommended for most uses</p>
                    </div>
                    <div class="mt-3">
                        <span class="bg-purple-500 text-white text-xs px-2 py-1 rounded">Recommended</span>
                    </div>
                </div>
            </div>
            
            <!-- High Compression -->
            <div class="compression-option border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-purple-300 transition-colors" data-level="high">
                <div class="text-center">
                    <i class="fas fa-compress text-3xl text-orange-500 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">High Compression</h3>
                    <p class="text-sm text-gray-600 mb-3">Smallest size, some quality loss</p>
                    <div class="text-xs text-gray-500">
                        <p>• Noticeable quality loss</p>
                        <p>• 50-80% size reduction</p>
                        <p>• Good for web sharing</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Advanced Options -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Advanced Options</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="compressImages" checked class="mr-2">
                    Compress images
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="removeMetadata" class="mr-2">
                    Remove metadata
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="optimizeFonts" checked class="mr-2">
                    Optimize fonts
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="removeAnnotations" class="mr-2">
                    Remove annotations
                </label>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Size
            </button>
            <button id="compressBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-compress-alt mr-2"></i>Compress PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Compressing your PDF file...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully compressed!</p>
            
            <!-- Compression Stats -->
            <div id="compressionStats" class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-lg font-bold text-gray-800" id="originalSize">0 MB</div>
                        <div class="text-sm text-gray-600">Original Size</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-green-600" id="compressedSize">0 MB</div>
                        <div class="text-sm text-gray-600">Compressed Size</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-purple-600" id="savedPercentage">0%</div>
                        <div class="text-sm text-gray-600">Size Reduction</div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newCompressionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Compress Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-purple-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-purple-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-purple-700">
            <li>Upload your PDF file</li>
            <li>Choose compression level (Low, Medium, or High)</li>
            <li>Configure advanced options if needed</li>
            <li>Preview estimated size reduction</li>
            <li>Compress your PDF and download the smaller file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> Medium compression offers the best balance between file size and quality. 
                Use high compression only when file size is more important than quality.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let selectedLevel = 'medium';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Compression level selection
        document.querySelectorAll('.compression-option').forEach(option => {
            option.addEventListener('click', function() {
                selectCompressionLevel(this.dataset.level);
            });
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewCompression);
        document.getElementById('compressBtn').addEventListener('click', compressPDF);
        document.getElementById('newCompressionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('compressionOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function selectCompressionLevel(level) {
        selectedLevel = level;
        
        // Update UI
        document.querySelectorAll('.compression-option').forEach(option => {
            option.classList.remove('border-purple-500', 'bg-purple-50');
            option.classList.add('border-gray-200');
        });
        
        const selectedOption = document.querySelector(`[data-level="${level}"]`);
        selectedOption.classList.remove('border-gray-200');
        selectedOption.classList.add('border-purple-500', 'bg-purple-50');
    }
    
    function previewCompression() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const originalSize = selectedFile.size / 1024 / 1024;
        let estimatedReduction;
        
        switch (selectedLevel) {
            case 'low':
                estimatedReduction = 0.2; // 20% reduction
                break;
            case 'medium':
                estimatedReduction = 0.4; // 40% reduction
                break;
            case 'high':
                estimatedReduction = 0.65; // 65% reduction
                break;
        }
        
        const newSize = originalSize * (1 - estimatedReduction);
        const savedMB = originalSize - newSize;
        const savedPercent = Math.round(estimatedReduction * 100);
        
        alert(`Estimated Compression Results:
        
Original Size: ${originalSize.toFixed(2)} MB
Estimated New Size: ${newSize.toFixed(2)} MB
Estimated Savings: ${savedMB.toFixed(2)} MB (${savedPercent}%)

Note: Actual results may vary based on PDF content.`);
    }
    
    function compressPDF() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('compression_level', selectedLevel);
        formData.append('compress_images', document.getElementById('compressImages').checked);
        formData.append('remove_metadata', document.getElementById('removeMetadata').checked);
        formData.append('optimize_fonts', document.getElementById('optimizeFonts').checked);
        formData.append('remove_annotations', document.getElementById('removeAnnotations').checked);
        
        // Show progress
        document.getElementById('compressionOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/compress_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Compression failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Calculate compression stats
            const originalSize = selectedFile.size / 1024 / 1024;
            const compressedSize = blob.size / 1024 / 1024;
            const savedPercent = Math.round(((originalSize - compressedSize) / originalSize) * 100);
            
            document.getElementById('originalSize').textContent = originalSize.toFixed(2) + ' MB';
            document.getElementById('compressedSize').textContent = compressedSize.toFixed(2) + ' MB';
            document.getElementById('savedPercentage').textContent = savedPercent + '%';
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'compressed.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error compressing PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('compressionOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        selectedLevel = 'medium';
        
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('compressionOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset compression level selection
        selectCompressionLevel('medium');
        
        // Reset checkboxes
        document.getElementById('compressImages').checked = true;
        document.getElementById('removeMetadata').checked = false;
        document.getElementById('optimizeFonts').checked = true;
        document.getElementById('removeAnnotations').checked = false;
    }
</script>
{% endblock %}
