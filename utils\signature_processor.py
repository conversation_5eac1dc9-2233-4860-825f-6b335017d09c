import fitz  # PyMuPDF
from PIL import Image, ImageDraw, ImageFont
import io
import os
import tempfile
import uuid
from datetime import datetime
import base64

class SignatureProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def add_text_signature(self, file, signature_text, position=(100, 100), 
                          font_size=24, color=(0, 0, 1), page_num=None):
        """Add text signature to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Determine which pages to sign
        if page_num is None:
            pages_to_sign = [pdf_doc.page_count - 1]  # Last page by default
        else:
            pages_to_sign = [page_num - 1]  # Convert to 0-based index
        
        for page_index in pages_to_sign:
            if page_index < pdf_doc.page_count:
                page = pdf_doc[page_index]
                
                # Create signature with script-like font
                page.insert_text(
                    position,
                    signature_text,
                    fontsize=font_size,
                    color=color,
                    fontname="helv-ita",  # Italic for script-like appearance
                    overlay=True
                )
        
        output_path = os.path.join(self.temp_dir, f'text_signed_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_image_signature(self, file, signature_image, position=(100, 100), 
                           scale=1.0, page_num=None):
        """Add image signature to PDF"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Process signature image
        img_data = signature_image.read()
        img = Image.open(io.BytesIO(img_data))
        
        # Ensure image has transparency
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Scale image
        if scale != 1.0:
            new_width = int(img.width * scale)
            new_height = int(img.height * scale)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Save processed signature
        sig_path = os.path.join(self.temp_dir, f'signature_{uuid.uuid4().hex}.png')
        img.save(sig_path)
        
        # Determine which pages to sign
        if page_num is None:
            pages_to_sign = [pdf_doc.page_count - 1]  # Last page by default
        else:
            pages_to_sign = [page_num - 1]  # Convert to 0-based index
        
        for page_index in pages_to_sign:
            if page_index < pdf_doc.page_count:
                page = pdf_doc[page_index]
                
                # Insert signature image
                img_rect = fitz.Rect(
                    position[0], 
                    position[1], 
                    position[0] + img.width, 
                    position[1] + img.height
                )
                page.insert_image(img_rect, filename=sig_path)
        
        output_path = os.path.join(self.temp_dir, f'image_signed_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_date_signature(self, file, date_format="%Y-%m-%d", position=(100, 150), 
                          font_size=12, color=(0, 0, 0), page_num=None):
        """Add date to signature area"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Get current date
        current_date = datetime.now().strftime(date_format)
        date_text = f"Date: {current_date}"
        
        # Determine which pages to sign
        if page_num is None:
            pages_to_sign = [pdf_doc.page_count - 1]  # Last page by default
        else:
            pages_to_sign = [page_num - 1]  # Convert to 0-based index
        
        for page_index in pages_to_sign:
            if page_index < pdf_doc.page_count:
                page = pdf_doc[page_index]
                
                # Insert date text
                page.insert_text(
                    position,
                    date_text,
                    fontsize=font_size,
                    color=color,
                    overlay=True
                )
        
        output_path = os.path.join(self.temp_dir, f'date_signed_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def add_complete_signature(self, file, signature_data):
        """Add complete signature with text, image, and date"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        page_num = signature_data.get('page_num', pdf_doc.page_count)
        page_index = page_num - 1
        
        if page_index >= pdf_doc.page_count:
            page_index = pdf_doc.page_count - 1
        
        page = pdf_doc[page_index]
        page_rect = page.rect
        
        # Base position for signature block
        base_x = signature_data.get('x', 100)
        base_y = signature_data.get('y', page_rect.height - 150)
        
        # Add signature line
        if signature_data.get('add_line', True):
            line_width = signature_data.get('line_width', 200)
            line_y = base_y + 40
            
            # Draw signature line
            line_start = fitz.Point(base_x, line_y)
            line_end = fitz.Point(base_x + line_width, line_y)
            page.draw_line(line_start, line_end, color=(0, 0, 0), width=1)
        
        # Add signature text
        if signature_data.get('signature_text'):
            text_y = base_y + 20
            page.insert_text(
                (base_x, text_y),
                signature_data['signature_text'],
                fontsize=signature_data.get('text_size', 16),
                color=signature_data.get('text_color', (0, 0, 1)),
                fontname="helv-ita",
                overlay=True
            )
        
        # Add signature image
        if signature_data.get('signature_image'):
            img_data = signature_data['signature_image'].read()
            img = Image.open(io.BytesIO(img_data))
            
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Scale and position image
            scale = signature_data.get('image_scale', 0.5)
            new_width = int(img.width * scale)
            new_height = int(img.height * scale)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Save and insert image
            sig_path = os.path.join(self.temp_dir, f'signature_{uuid.uuid4().hex}.png')
            img.save(sig_path)
            
            img_y = base_y - new_height + 15
            img_rect = fitz.Rect(base_x, img_y, base_x + new_width, img_y + new_height)
            page.insert_image(img_rect, filename=sig_path)
        
        # Add date
        if signature_data.get('add_date', True):
            date_format = signature_data.get('date_format', '%Y-%m-%d')
            current_date = datetime.now().strftime(date_format)
            date_text = f"Date: {current_date}"
            
            date_y = base_y + 60
            page.insert_text(
                (base_x, date_y),
                date_text,
                fontsize=signature_data.get('date_size', 10),
                color=signature_data.get('date_color', (0, 0, 0)),
                overlay=True
            )
        
        # Add signature label
        if signature_data.get('signature_label'):
            label_y = base_y + 80
            page.insert_text(
                (base_x, label_y),
                signature_data['signature_label'],
                fontsize=signature_data.get('label_size', 8),
                color=signature_data.get('label_color', (0.5, 0.5, 0.5)),
                overlay=True
            )
        
        output_path = os.path.join(self.temp_dir, f'complete_signed_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def create_signature_field(self, file, field_data):
        """Create signature field with border and label"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        page_num = field_data.get('page_num', pdf_doc.page_count)
        page_index = page_num - 1
        
        if page_index >= pdf_doc.page_count:
            page_index = pdf_doc.page_count - 1
        
        page = pdf_doc[page_index]
        
        # Field dimensions
        x = field_data.get('x', 100)
        y = field_data.get('y', 100)
        width = field_data.get('width', 200)
        height = field_data.get('height', 60)
        
        # Draw field border
        field_rect = fitz.Rect(x, y, x + width, y + height)
        page.draw_rect(field_rect, color=(0, 0, 0), width=1)
        
        # Add field label
        if field_data.get('label'):
            label_y = y - 15
            page.insert_text(
                (x, label_y),
                field_data['label'],
                fontsize=field_data.get('label_size', 10),
                color=(0, 0, 0),
                overlay=True
            )
        
        # Add placeholder text
        if field_data.get('placeholder'):
            placeholder_x = x + 5
            placeholder_y = y + height/2 + 5
            page.insert_text(
                (placeholder_x, placeholder_y),
                field_data['placeholder'],
                fontsize=field_data.get('placeholder_size', 8),
                color=(0.7, 0.7, 0.7),
                overlay=True
            )
        
        output_path = os.path.join(self.temp_dir, f'signature_field_{uuid.uuid4().hex}.pdf')
        pdf_doc.save(output_path)
        pdf_doc.close()
        
        return output_path
    
    def get_signature_positions(self, file):
        """Get suggested signature positions based on document content"""
        pdf_bytes = file.read()
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        positions = []
        
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            page_rect = page.rect
            
            # Common signature positions
            positions.append({
                'page': page_num + 1,
                'name': 'Bottom Right',
                'x': page_rect.width - 250,
                'y': page_rect.height - 100,
                'description': 'Traditional signature position'
            })
            
            positions.append({
                'page': page_num + 1,
                'name': 'Bottom Left',
                'x': 50,
                'y': page_rect.height - 100,
                'description': 'Left-aligned signature'
            })
            
            positions.append({
                'page': page_num + 1,
                'name': 'Bottom Center',
                'x': (page_rect.width - 200) / 2,
                'y': page_rect.height - 100,
                'description': 'Centered signature'
            })
        
        pdf_doc.close()
        return positions
