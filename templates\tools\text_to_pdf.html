{% extends "base.html" %}

{% block title %}{{ t.tools_list.text_to_pdf.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-gray-500 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-file-alt text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.text_to_pdf.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.text_to_pdf.description }}</p>
    </div>
    
    <!-- Text Input Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div class="mb-6">
            <label class="block text-lg font-semibold text-gray-800 mb-4">Enter Your Text:</label>
            <textarea id="textInput" 
                      placeholder="Type or paste your text here..."
                      class="w-full h-64 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                      style="font-family: {{ 'Cairo, sans-serif' if lang == 'ar' else 'Inter, sans-serif' }}"></textarea>
        </div>
        
        <!-- Text Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Font Family</label>
                <select id="fontFamily" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="Helvetica">Helvetica</option>
                    <option value="Times-Roman">Times Roman</option>
                    <option value="Courier">Courier</option>
                    <option value="Arial" {{ 'selected' if lang == 'ar' else '' }}>Arial</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                <select id="fontSize" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="8">8pt</option>
                    <option value="10">10pt</option>
                    <option value="12" selected>12pt</option>
                    <option value="14">14pt</option>
                    <option value="16">16pt</option>
                    <option value="18">18pt</option>
                    <option value="20">20pt</option>
                    <option value="24">24pt</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Page Size</label>
                <select id="pageSize" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="A4" selected>A4</option>
                    <option value="Letter">Letter</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Line Spacing</label>
                <select id="lineSpacing" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="1.0">Single</option>
                    <option value="1.2" selected>1.2</option>
                    <option value="1.5">1.5</option>
                    <option value="2.0">Double</option>
                </select>
            </div>
        </div>
        
        <!-- Advanced Options -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-md font-semibold text-gray-800 mb-4">Advanced Options:</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Document Title</label>
                    <input type="text" id="docTitle" placeholder="Optional document title" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Author</label>
                    <input type="text" id="docAuthor" placeholder="Optional author name" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <input type="text" id="docSubject" placeholder="Optional subject" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>
        
        <!-- Text Statistics -->
        <div class="bg-blue-50 rounded-lg p-4 mb-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-lg font-bold text-blue-600" id="charCount">0</div>
                    <div class="text-sm text-gray-600">Characters</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-blue-600" id="wordCount">0</div>
                    <div class="text-sm text-gray-600">Words</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-blue-600" id="lineCount">0</div>
                    <div class="text-sm text-gray-600">Lines</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-blue-600" id="paraCount">0</div>
                    <div class="text-sm text-gray-600">Paragraphs</div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4">
            <button id="clearBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eraser mr-2"></i>Clear Text
            </button>
            <button id="previewBtn" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview
            </button>
            <button id="convertBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-file-pdf mr-2"></i>Convert to PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-gray-200 border-t-gray-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Converting your text to PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your text has been successfully converted to PDF!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newConversionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Convert More Text
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-gray-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-gray-700">
            <li>Type or paste your text in the text area above</li>
            <li>Choose your preferred font family, size, and page settings</li>
            <li>Optionally add document metadata (title, author, subject)</li>
            <li>Click "Convert to PDF" to generate your document</li>
            <li>Download your PDF file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> Use markdown-style formatting like # for headings to create better-formatted documents.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        updateStats();
    });
    
    function setupEventListeners() {
        const textInput = document.getElementById('textInput');
        
        // Update stats on text change
        textInput.addEventListener('input', updateStats);
        
        // Button events
        document.getElementById('clearBtn').addEventListener('click', clearText);
        document.getElementById('previewBtn').addEventListener('click', previewText);
        document.getElementById('convertBtn').addEventListener('click', convertToPDF);
        document.getElementById('newConversionBtn').addEventListener('click', resetForm);
    }
    
    function updateStats() {
        const text = document.getElementById('textInput').value;
        
        const charCount = text.length;
        const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
        const lineCount = text.split('\n').length;
        const paraCount = text.trim() ? text.trim().split(/\n\s*\n/).length : 0;
        
        document.getElementById('charCount').textContent = charCount;
        document.getElementById('wordCount').textContent = wordCount;
        document.getElementById('lineCount').textContent = lineCount;
        document.getElementById('paraCount').textContent = paraCount;
    }
    
    function clearText() {
        document.getElementById('textInput').value = '';
        updateStats();
    }
    
    function previewText() {
        const text = document.getElementById('textInput').value;
        if (!text.trim()) {
            alert('Please enter some text first.');
            return;
        }
        
        // Create a simple preview window
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>Text Preview</title>
                    <style>
                        body {
                            font-family: ${document.getElementById('fontFamily').value}, sans-serif;
                            font-size: ${document.getElementById('fontSize').value}pt;
                            line-height: ${document.getElementById('lineSpacing').value};
                            margin: 72px;
                            white-space: pre-wrap;
                        }
                    </style>
                </head>
                <body>${text}</body>
            </html>
        `);
        previewWindow.document.close();
    }
    
    function convertToPDF() {
        const text = document.getElementById('textInput').value;
        if (!text.trim()) {
            alert('Please enter some text to convert.');
            return;
        }
        
        const formData = new FormData();
        
        // Check if we should use formatted PDF
        const title = document.getElementById('docTitle').value;
        const author = document.getElementById('docAuthor').value;
        const subject = document.getElementById('docSubject').value;
        
        if (title || author || subject) {
            // Use formatted PDF endpoint
            formData.append('title', title);
            formData.append('content', text);
            formData.append('author', author);
            formData.append('subject', subject);
            
            convertWithEndpoint('/api/create_formatted_pdf', formData, 'formatted_document.pdf');
        } else {
            // Use simple text to PDF endpoint
            formData.append('text', text);
            formData.append('font_family', document.getElementById('fontFamily').value);
            formData.append('font_size', document.getElementById('fontSize').value);
            formData.append('page_size', document.getElementById('pageSize').value);
            
            convertWithEndpoint('/api/text_to_pdf', formData, 'text_document.pdf');
        }
    }
    
    function convertWithEndpoint(endpoint, formData, filename) {
        // Show progress
        document.querySelector('.bg-white.rounded-xl.shadow-lg.p-8.mb-8').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch(endpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Conversion failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
            };
        })
        .catch(error => {
            alert('Error converting text: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.querySelector('.bg-white.rounded-xl.shadow-lg.p-8.mb-8').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        document.getElementById('textInput').value = '';
        document.getElementById('docTitle').value = '';
        document.getElementById('docAuthor').value = '';
        document.getElementById('docSubject').value = '';
        
        document.getElementById('resultSection').classList.add('hidden');
        document.querySelector('.bg-white.rounded-xl.shadow-lg.p-8.mb-8').classList.remove('hidden');
        
        updateStats();
    }
</script>
{% endblock %}
