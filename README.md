# PDF2Any - Professional PDF Processing Web Application

A comprehensive web application for PDF processing with 25 professional tools, bilingual support (English/Arabic), and modern responsive design.

## 🌟 Features

### Core PDF Tools (25 Tools)
1. **Add Page Number** - Add customizable page numbers to PDF documents
2. **Merge PDF** - Combine multiple PDF files into one document
3. **Protect PDF** - Add password protection to secure PDF files
4. **Compress PDF** - Reduce PDF file size while maintaining quality
5. **Remove Page from PDF** - Delete specific pages from PDF documents
6. **Split PDF** - Split PDF into individual pages or page ranges
7. **Extract Page from PDF** - Extract specific pages to create new PDF
8. **Extract Image from PDF** - Extract all images from PDF documents
9. **Text to PDF** - Convert plain text files to PDF format
10. **Rotate PDF** - Rotate PDF pages by 90, 180, or 270 degrees
11. **Unlock PDF** - Remove password protection from PDF files
12. **Add Watermark** - Add text, image, or stamp watermarks
13. **PDF to Image** - Convert PDF pages to high-quality images
14. **Image to PDF** - Convert images to PDF format
15. **Image to QR** - Generate QR codes from images or text
16. **PDF to OCR** - Extract text from PDF using OCR technology
17. **Translate PDF** - Translate PDF text using Google Translate
18. **Edit PDF** - Edit PDF with text, images, and drawing tools
19. **Crop PDF** - Crop PDF pages to remove unwanted areas
20. **PNG to PDF** - Convert PNG images to PDF format
21. **PDF to PNG** - Convert PDF pages to PNG images
22. **Sign PDF** - Add digital signatures to PDF documents
23. **GIF to PDF** - Convert GIF animations to PDF format
24. **PDF to GIF** - Convert PDF pages to GIF animation
25. **Organize PDF** - Reorder, rotate, and manage PDF pages

### Advanced Features

#### 🎨 Advanced Watermarking System
- **Text Watermarks**: Customizable text with font size, opacity, color, rotation, and positioning
- **Image Watermarks**: Upload custom images with scale, opacity, rotation controls
- **Ready-made Stamps**: 16 professional stamps including:
  - Success: APPROVED, CERTIFIED, ORIGINAL, PASS, PAID
  - Error: REJECTED, CANCELED, DO NOT COPY
  - Warning: PENDING, URGENT, SAMPLE
  - Info: CONFIDENTIAL, CLASSIFIED, TOP SECRET
  - Neutral: COPYRIGHT, COPY

#### 🌐 Bilingual Support
- Full English and Arabic language support
- RTL (Right-to-Left) layout for Arabic
- Instant language switching
- Localized UI and content

#### 🎯 PDF Translation
- Extract text from PDF documents
- Automatic language detection
- Google Translate integration
- Chunk-based translation for large documents
- HTML interface for offline translation work

#### 📝 Text Processing
- Convert text to professionally formatted PDF
- Multiple font families and sizes
- Custom page layouts and margins
- Document metadata support
- Multi-column layouts
- Table creation

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd pdf2any
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python run.py
   ```

4. **Open your browser**
   Navigate to `http://localhost:5000`

## 🛠️ Technology Stack

### Backend
- **Flask** - Python web framework
- **PyMuPDF (fitz)** - PDF processing and manipulation
- **PyPDF2** - Additional PDF operations
- **ReportLab** - PDF generation and creation
- **Pillow (PIL)** - Image processing
- **QRCode** - QR code generation

### Frontend
- **HTML5** - Modern semantic markup
- **Tailwind CSS** - Utility-first CSS framework
- **JavaScript** - Interactive functionality
- **PDF.js** - Client-side PDF rendering
- **Font Awesome** - Professional icons

### Features
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Drag & Drop** - Intuitive file upload interface
- **Progress Indicators** - Real-time processing feedback
- **Error Handling** - Comprehensive error management
- **File Security** - Automatic cleanup of temporary files

## 📁 Project Structure

```
pdf2any/
├── app.py                 # Main Flask application
├── run.py                 # Application launcher
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   ├── index.html        # Homepage
│   └── tools/            # Tool-specific templates
├── translations/         # Language files
│   ├── en.json          # English translations
│   └── ar.json          # Arabic translations
├── utils/               # Utility modules
│   ├── pdf_processor.py # PDF processing functions
│   ├── image_processor.py # Image processing functions
│   ├── watermark_processor.py # Watermark functions
│   ├── translator.py    # Translation utilities
│   └── text_processor.py # Text processing functions
├── static/              # Static assets
├── uploads/             # Temporary upload directory
└── processed/           # Temporary processed files
```

## 🔧 Configuration

### Environment Variables
- `FLASK_ENV` - Set to `development` for debug mode
- `FLASK_DEBUG` - Set to `1` for debug mode
- `SECRET_KEY` - Flask secret key (auto-generated)

### File Limits
- Maximum file size: 50MB
- Supported formats: PDF, PNG, JPG, JPEG, GIF, BMP, TIFF
- Automatic cleanup of temporary files

## 🌍 Internationalization

The application supports multiple languages:

- **English** (`en`) - Default language
- **Arabic** (`ar`) - Full RTL support

To add a new language:
1. Create a new JSON file in `translations/` directory
2. Copy the structure from `en.json`
3. Translate all text strings
4. Add the language option to the UI

## 🔒 Security Features

- **File Validation** - Strict file type checking
- **Temporary Files** - Automatic cleanup after processing
- **Input Sanitization** - Protection against malicious inputs
- **Error Handling** - Secure error messages
- **No Data Storage** - Files are not permanently stored

## 🎨 UI/UX Features

- **Modern Design** - Clean, professional interface
- **Responsive Layout** - Optimized for all screen sizes
- **Intuitive Navigation** - Easy-to-use tool organization
- **Visual Feedback** - Progress indicators and animations
- **Accessibility** - WCAG compliant design
- **Dark/Light Themes** - Automatic theme detection

## 📱 Mobile Support

- Fully responsive design
- Touch-friendly interface
- Mobile-optimized file upload
- Swipe gestures support
- Optimized for iOS and Android

## 🔄 API Endpoints

The application provides RESTful API endpoints for all tools:

- `POST /api/merge_pdf` - Merge PDF files
- `POST /api/split_pdf` - Split PDF files
- `POST /api/compress_pdf` - Compress PDF files
- `POST /api/protect_pdf` - Add password protection
- `POST /api/add_watermark` - Add watermarks
- And many more...

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🙏 Acknowledgments

- PyMuPDF team for excellent PDF processing capabilities
- ReportLab team for PDF generation tools
- Tailwind CSS for the beautiful UI framework
- Font Awesome for professional icons
- Google Translate for translation services

---

**PDF2Any** - Transform your PDFs with professional tools! 🚀
