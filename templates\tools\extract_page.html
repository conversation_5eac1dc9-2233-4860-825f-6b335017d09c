{% extends "base.html" %}

{% block title %}Extract Pages - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-file-export text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Extract Pages from PDF</h1>
        <p class="text-xl text-gray-600">Extract specific pages and create a new PDF</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to extract pages from</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Extract Options -->
    <div id="extractOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Extract Pages</h2>
        
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Pages to Extract</label>
            <input type="text" id="pagesToExtract" placeholder="e.g., 1,3,5-10,15" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
            <p class="text-sm text-gray-500 mt-1">Enter page numbers separated by commas. Use hyphens for ranges.</p>
        </div>
        
        <!-- Quick Options -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-md font-semibold text-gray-800 mb-3">Quick Options:</h3>
            <div class="flex flex-wrap gap-2">
                <button onclick="setPages('1')" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors text-sm">
                    First Page Only
                </button>
                <button onclick="setPages('1-5')" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors text-sm">
                    First 5 Pages
                </button>
                <button onclick="setPages('last')" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors text-sm">
                    Last Page Only
                </button>
                <button onclick="setPages('odd')" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors text-sm">
                    Odd Pages
                </button>
                <button onclick="setPages('even')" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors text-sm">
                    Even Pages
                </button>
            </div>
        </div>
        
        <!-- Output Options -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Output Filename</label>
            <input type="text" id="outputName" value="extracted_pages" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview
            </button>
            <button id="extractBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-file-export mr-2"></i>Extract Pages
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-teal-200 border-t-teal-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Extracting pages from your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Pages have been successfully extracted!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newExtractionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Extract From Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-teal-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-teal-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-teal-700">
            <li>Upload your PDF file</li>
            <li>Specify which pages to extract using page numbers</li>
            <li>Use commas to separate individual pages (e.g., 1,3,5)</li>
            <li>Use hyphens for page ranges (e.g., 5-10)</li>
            <li>Set output filename for the extracted PDF</li>
            <li>Extract pages and download the new PDF</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewExtraction);
        document.getElementById('extractBtn').addEventListener('click', extractPages);
        document.getElementById('newExtractionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('extractOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function setPages(type) {
        const input = document.getElementById('pagesToExtract');
        input.value = type;
    }
    
    function previewExtraction() {
        const pages = document.getElementById('pagesToExtract').value;
        if (!pages.trim()) {
            alert('Please specify which pages to extract.');
            return;
        }
        
        alert(`Preview: The following pages will be extracted: ${pages}\n\nA new PDF will be created containing only these pages.`);
    }
    
    function extractPages() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const pages = document.getElementById('pagesToExtract').value;
        if (!pages.trim()) {
            alert('Please specify which pages to extract.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('pages_to_extract', pages);
        formData.append('output_name', document.getElementById('outputName').value);
        
        // Show progress
        document.getElementById('extractOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/extract_pages', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Page extraction failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = document.getElementById('outputName').value + '.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error extracting pages: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('extractOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('extractOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        document.getElementById('pagesToExtract').value = '';
        document.getElementById('outputName').value = 'extracted_pages';
    }
</script>
{% endblock %}
