{% extends "base.html" %}

{% block title %}{{ t.tools_list.add_watermark.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-tint text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.add_watermark.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.add_watermark.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to add watermark</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Watermark Options -->
    <div id="watermarkOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Watermark Options</h2>
        
        <!-- Watermark Type Tabs -->
        <div class="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-8">
            <button id="textTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors bg-white text-blue-600 shadow-sm">
                <i class="fas fa-font mr-2"></i>{{ t.watermark.text_watermark }}
            </button>
            <button id="imageTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors text-gray-600 hover:text-gray-800">
                <i class="fas fa-image mr-2"></i>{{ t.watermark.image_watermark }}
            </button>
            <button id="stampTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors text-gray-600 hover:text-gray-800">
                <i class="fas fa-stamp mr-2"></i>{{ t.watermark.stamps }}
            </button>
        </div>
        
        <!-- Text Watermark Panel -->
        <div id="textPanel" class="watermark-panel">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.text }}</label>
                    <input type="text" id="watermarkText" value="WATERMARK" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.font_size }}</label>
                    <input type="range" id="fontSize" min="20" max="100" value="50" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="fontSizeValue">50</span>px</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.opacity }}</label>
                    <input type="range" id="textOpacity" min="0.1" max="1" step="0.1" value="0.5" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="textOpacityValue">50</span>%</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.color }}</label>
                    <input type="color" id="textColor" value="#000000" class="w-full h-10 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.rotation }}</label>
                    <input type="range" id="textRotation" min="0" max="360" value="45" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="textRotationValue">45</span>°</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.position }}</label>
                    <select id="textPosition" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="center">Center</option>
                        <option value="top-left">Top Left</option>
                        <option value="top-center">Top Center</option>
                        <option value="top-right">Top Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="bottom-center">Bottom Center</option>
                        <option value="bottom-right">Bottom Right</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Image Watermark Panel -->
        <div id="imagePanel" class="watermark-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="md:col-span-2 lg:col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Watermark Image</label>
                    <input type="file" id="watermarkImage" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.scale }}</label>
                    <input type="range" id="imageScale" min="0.1" max="2" step="0.1" value="0.5" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="imageScaleValue">50</span>%</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.opacity }}</label>
                    <input type="range" id="imageOpacity" min="0.1" max="1" step="0.1" value="0.5" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="imageOpacityValue">50</span>%</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.rotation }}</label>
                    <input type="range" id="imageRotation" min="0" max="360" value="0" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="imageRotationValue">0</span>°</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.position }}</label>
                    <select id="imagePosition" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="center">Center</option>
                        <option value="top-left">Top Left</option>
                        <option value="top-center">Top Center</option>
                        <option value="top-right">Top Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="bottom-center">Bottom Center</option>
                        <option value="bottom-right">Bottom Right</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Stamp Panel -->
        <div id="stampPanel" class="watermark-panel hidden">
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-4">Select Stamp:</label>
                <div id="stampGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- Stamps will be loaded here -->
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.opacity }}</label>
                    <input type="range" id="stampOpacity" min="0.1" max="1" step="0.1" value="0.7" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="stampOpacityValue">70</span>%</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.scale }}</label>
                    <input type="range" id="stampScale" min="0.5" max="2" step="0.1" value="1" class="w-full">
                    <div class="text-center text-sm text-gray-600 mt-1"><span id="stampScaleValue">100</span>%</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ t.watermark.position }}</label>
                    <select id="stampPosition" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="center">Center</option>
                        <option value="top-left">Top Left</option>
                        <option value="top-center">Top Center</option>
                        <option value="top-right">Top Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="bottom-center">Bottom Center</option>
                        <option value="bottom-right">Bottom Right</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Common Options -->
        <div class="mt-8 p-6 bg-gray-50 rounded-lg">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Apply to Pages:</h4>
            <div class="flex space-x-4">
                <label class="flex items-center">
                    <input type="radio" name="pageRange" value="all" checked class="mr-2">
                    All Pages
                </label>
                <label class="flex items-center">
                    <input type="radio" name="pageRange" value="specific" class="mr-2">
                    Specific Pages
                </label>
            </div>
            <input type="text" id="specificPages" placeholder="e.g., 1,3,5-10" class="mt-2 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 hidden">
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview
            </button>
            <button id="applyWatermarkBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-tint mr-2"></i>Apply Watermark
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Adding watermark to your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Watermark has been successfully added to your PDF!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newWatermarkBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Add Another Watermark
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let selectedStamp = null;
    let currentTab = 'text';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        loadStamps();
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Tab switching
        document.getElementById('textTab').addEventListener('click', () => switchTab('text'));
        document.getElementById('imageTab').addEventListener('click', () => switchTab('image'));
        document.getElementById('stampTab').addEventListener('click', () => switchTab('stamp'));
        
        // Range inputs
        setupRangeInputs();
        
        // Page range
        document.querySelectorAll('input[name="pageRange"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const specificPages = document.getElementById('specificPages');
                if (this.value === 'specific') {
                    specificPages.classList.remove('hidden');
                } else {
                    specificPages.classList.add('hidden');
                }
            });
        });
        
        // Apply watermark
        document.getElementById('applyWatermarkBtn').addEventListener('click', applyWatermark);
        
        // New watermark
        document.getElementById('newWatermarkBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('watermarkOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function switchTab(tab) {
        // Update tab buttons
        document.querySelectorAll('[id$="Tab"]').forEach(btn => {
            btn.classList.remove('bg-white', 'text-blue-600', 'shadow-sm');
            btn.classList.add('text-gray-600');
        });
        
        document.getElementById(tab + 'Tab').classList.add('bg-white', 'text-blue-600', 'shadow-sm');
        document.getElementById(tab + 'Tab').classList.remove('text-gray-600');
        
        // Update panels
        document.querySelectorAll('.watermark-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        document.getElementById(tab + 'Panel').classList.remove('hidden');
        currentTab = tab;
    }
    
    function setupRangeInputs() {
        const ranges = [
            { id: 'fontSize', valueId: 'fontSizeValue', suffix: 'px' },
            { id: 'textOpacity', valueId: 'textOpacityValue', suffix: '%', multiplier: 100 },
            { id: 'textRotation', valueId: 'textRotationValue', suffix: '°' },
            { id: 'imageScale', valueId: 'imageScaleValue', suffix: '%', multiplier: 100 },
            { id: 'imageOpacity', valueId: 'imageOpacityValue', suffix: '%', multiplier: 100 },
            { id: 'imageRotation', valueId: 'imageRotationValue', suffix: '°' },
            { id: 'stampOpacity', valueId: 'stampOpacityValue', suffix: '%', multiplier: 100 },
            { id: 'stampScale', valueId: 'stampScaleValue', suffix: '%', multiplier: 100 }
        ];
        
        ranges.forEach(range => {
            const input = document.getElementById(range.id);
            const valueDisplay = document.getElementById(range.valueId);
            
            if (input && valueDisplay) {
                input.addEventListener('input', function() {
                    let value = this.value;
                    if (range.multiplier) {
                        value = Math.round(value * range.multiplier);
                    }
                    valueDisplay.textContent = value;
                });
            }
        });
    }
    
    function loadStamps() {
        fetch('/api/get_stamps')
            .then(response => response.json())
            .then(data => {
                const stampGrid = document.getElementById('stampGrid');
                stampGrid.innerHTML = '';
                
                data.stamps.forEach(stamp => {
                    const stampBtn = document.createElement('button');
                    stampBtn.className = 'stamp-btn p-3 border-2 border-gray-300 rounded-lg text-sm font-medium hover:border-blue-500 transition-colors';
                    stampBtn.textContent = stamp;
                    stampBtn.onclick = () => selectStamp(stamp, stampBtn);
                    stampGrid.appendChild(stampBtn);
                });
            })
            .catch(error => console.error('Error loading stamps:', error));
    }
    
    function selectStamp(stamp, button) {
        // Remove selection from other stamps
        document.querySelectorAll('.stamp-btn').forEach(btn => {
            btn.classList.remove('border-blue-500', 'bg-blue-50');
            btn.classList.add('border-gray-300');
        });
        
        // Select current stamp
        button.classList.add('border-blue-500', 'bg-blue-50');
        button.classList.remove('border-gray-300');
        selectedStamp = stamp;
    }
    
    function applyWatermark() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        // Get page range
        const pageRange = document.querySelector('input[name="pageRange"]:checked').value;
        if (pageRange === 'specific') {
            const specificPages = document.getElementById('specificPages').value;
            formData.append('pages', specificPages);
        } else {
            formData.append('pages', 'all');
        }
        
        let apiEndpoint = '';
        
        if (currentTab === 'text') {
            apiEndpoint = '/api/add_text_watermark';
            formData.append('text', document.getElementById('watermarkText').value);
            formData.append('font_size', document.getElementById('fontSize').value);
            formData.append('opacity', document.getElementById('textOpacity').value);
            
            const color = document.getElementById('textColor').value;
            const r = parseInt(color.substr(1, 2), 16) / 255;
            const g = parseInt(color.substr(3, 2), 16) / 255;
            const b = parseInt(color.substr(5, 2), 16) / 255;
            formData.append('color', `${r},${g},${b}`);
            
            formData.append('rotation', document.getElementById('textRotation').value);
            formData.append('position', document.getElementById('textPosition').value);
        } else if (currentTab === 'image') {
            const imageFile = document.getElementById('watermarkImage').files[0];
            if (!imageFile) {
                alert('Please select an image for watermark.');
                return;
            }
            
            apiEndpoint = '/api/add_image_watermark';
            formData.append('image', imageFile);
            formData.append('scale', document.getElementById('imageScale').value);
            formData.append('opacity', document.getElementById('imageOpacity').value);
            formData.append('rotation', document.getElementById('imageRotation').value);
            formData.append('position', document.getElementById('imagePosition').value);
        } else if (currentTab === 'stamp') {
            if (!selectedStamp) {
                alert('Please select a stamp.');
                return;
            }
            
            apiEndpoint = '/api/add_stamp';
            formData.append('stamp_text', selectedStamp);
            formData.append('opacity', document.getElementById('stampOpacity').value);
            formData.append('scale', document.getElementById('stampScale').value);
            formData.append('position', document.getElementById('stampPosition').value);
        }
        
        // Show progress
        document.getElementById('watermarkOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch(apiEndpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Watermark application failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'watermarked.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error applying watermark: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('watermarkOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        selectedStamp = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('watermarkOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
    }
</script>
{% endblock %}
