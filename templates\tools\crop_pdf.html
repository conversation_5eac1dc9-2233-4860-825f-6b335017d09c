{% extends "base.html" %}

{% block title %}Crop PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-crop-alt text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.crop_pdf.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.crop_pdf.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to crop its pages</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Crop Options -->
    <div id="cropOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Crop Options</h2>
        
        <!-- Crop Method Tabs -->
        <div class="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-8">
            <button id="manualCropTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors bg-white text-yellow-600 shadow-sm">
                <i class="fas fa-hand-paper mr-2"></i>Manual Crop
            </button>
            <button id="autoCropTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors text-gray-600 hover:text-gray-800">
                <i class="fas fa-magic mr-2"></i>Auto Crop Margins
            </button>
            <button id="presetCropTab" class="flex-1 py-3 px-4 rounded-md font-medium transition-colors text-gray-600 hover:text-gray-800">
                <i class="fas fa-th-large mr-2"></i>Preset Crops
            </button>
        </div>
        
        <!-- Manual Crop Panel -->
        <div id="manualCropPanel" class="crop-panel">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Crop Controls -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Crop Dimensions</h3>
                    
                    <!-- Unit Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Measurement Unit</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="cropUnit" value="percent" checked class="mr-2">
                                Percentage (%)
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="cropUnit" value="pixels" class="mr-2">
                                Pixels (px)
                            </label>
                        </div>
                    </div>
                    
                    <!-- Crop Values -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Left Margin</label>
                            <input type="number" id="cropLeft" value="0" min="0" max="100" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Top Margin</label>
                            <input type="number" id="cropTop" value="0" min="0" max="100" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Right Margin</label>
                            <input type="number" id="cropRight" value="100" min="0" max="100" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bottom Margin</label>
                            <input type="number" id="cropBottom" value="100" min="0" max="100" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        </div>
                    </div>
                    
                    <!-- Quick Presets -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quick Presets</label>
                        <div class="grid grid-cols-2 gap-2">
                            <button onclick="setCropValues(10, 10, 90, 90)" class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm">
                                Remove 10% margins
                            </button>
                            <button onclick="setCropValues(15, 15, 85, 85)" class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm">
                                Remove 15% margins
                            </button>
                            <button onclick="setCropValues(0, 20, 100, 80)" class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm">
                                Remove header/footer
                            </button>
                            <button onclick="setCropValues(20, 0, 80, 100)" class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm">
                                Remove side margins
                            </button>
                        </div>
                    </div>
                    
                    <!-- Page Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Apply to Pages</label>
                        <div class="flex space-x-4 mb-2">
                            <label class="flex items-center">
                                <input type="radio" name="pageRange" value="all" checked class="mr-2">
                                All Pages
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="pageRange" value="specific" class="mr-2">
                                Specific Pages
                            </label>
                        </div>
                        <input type="text" id="specificPages" placeholder="e.g., 1,3,5-10" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 hidden">
                    </div>
                </div>
                
                <!-- Visual Preview -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Crop Preview</h3>
                    <div class="border-2 border-gray-300 rounded-lg p-4 bg-gray-50">
                        <div id="cropPreview" class="relative bg-white border-2 border-gray-400 mx-auto" style="width: 300px; height: 400px;">
                            <!-- Original page representation -->
                            <div class="absolute inset-0 bg-gray-100 border border-gray-300">
                                <div class="p-4 text-xs text-gray-500">
                                    <div class="mb-2">Original Page</div>
                                    <div class="border-b border-gray-300 mb-2"></div>
                                    <div class="space-y-1">
                                        <div class="h-2 bg-gray-300 rounded"></div>
                                        <div class="h-2 bg-gray-300 rounded w-3/4"></div>
                                        <div class="h-2 bg-gray-300 rounded w-1/2"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Crop area overlay -->
                            <div id="cropOverlay" class="absolute bg-yellow-200 bg-opacity-50 border-2 border-yellow-500 border-dashed">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-yellow-800 font-medium text-sm">Crop Area</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center text-sm text-gray-600">
                            <p>Yellow area shows what will be kept after cropping</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Auto Crop Panel -->
        <div id="autoCropPanel" class="crop-panel hidden">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-8">
                    <i class="fas fa-magic text-6xl text-yellow-500 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Automatic Margin Detection</h3>
                    <p class="text-gray-600">Automatically detect and remove white margins from your PDF pages</p>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Auto Crop Settings</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Sensitivity</label>
                            <input type="range" id="autoThreshold" min="0.8" max="0.99" step="0.01" value="0.95" class="w-full">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Less Sensitive</span>
                                <span id="thresholdValue">95%</span>
                                <span>More Sensitive</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Higher sensitivity detects smaller margins</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Margin</label>
                            <input type="number" id="minMargin" value="10" min="0" max="50" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500">
                            <p class="text-xs text-gray-500 mt-1">Minimum margin to keep (in pixels)</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                        <div>
                            <h5 class="font-medium text-blue-800 mb-1">How Auto Crop Works</h5>
                            <p class="text-blue-700 text-sm">
                                The system analyzes each page to detect white or near-white margins and automatically 
                                removes them while preserving the actual content. This is perfect for scanned documents 
                                or PDFs with excessive white space.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Preset Crop Panel -->
        <div id="presetCropPanel" class="crop-panel hidden">
            <div class="max-w-4xl mx-auto">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">Common Crop Presets</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Standard Document -->
                    <div class="preset-card bg-gray-50 border border-gray-200 rounded-lg p-6 cursor-pointer hover:bg-gray-100 transition-colors" 
                         onclick="applyPreset('document')">
                        <div class="text-center">
                            <i class="fas fa-file-alt text-3xl text-blue-600 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Standard Document</h4>
                            <p class="text-sm text-gray-600 mb-3">Remove 10% margins from all sides</p>
                            <div class="text-xs text-gray-500">
                                Left: 10%, Top: 10%<br>
                                Right: 90%, Bottom: 90%
                            </div>
                        </div>
                    </div>
                    
                    <!-- Header/Footer Removal -->
                    <div class="preset-card bg-gray-50 border border-gray-200 rounded-lg p-6 cursor-pointer hover:bg-gray-100 transition-colors" 
                         onclick="applyPreset('header-footer')">
                        <div class="text-center">
                            <i class="fas fa-align-center text-3xl text-green-600 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Remove Header/Footer</h4>
                            <p class="text-sm text-gray-600 mb-3">Keep content, remove top and bottom margins</p>
                            <div class="text-xs text-gray-500">
                                Left: 0%, Top: 15%<br>
                                Right: 100%, Bottom: 85%
                            </div>
                        </div>
                    </div>
                    
                    <!-- Side Margins -->
                    <div class="preset-card bg-gray-50 border border-gray-200 rounded-lg p-6 cursor-pointer hover:bg-gray-100 transition-colors" 
                         onclick="applyPreset('sides')">
                        <div class="text-center">
                            <i class="fas fa-arrows-alt-h text-3xl text-purple-600 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Remove Side Margins</h4>
                            <p class="text-sm text-gray-600 mb-3">Remove left and right margins only</p>
                            <div class="text-xs text-gray-500">
                                Left: 15%, Top: 0%<br>
                                Right: 85%, Bottom: 100%
                            </div>
                        </div>
                    </div>
                    
                    <!-- Book Scan -->
                    <div class="preset-card bg-gray-50 border border-gray-200 rounded-lg p-6 cursor-pointer hover:bg-gray-100 transition-colors" 
                         onclick="applyPreset('book')">
                        <div class="text-center">
                            <i class="fas fa-book text-3xl text-orange-600 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Book Scan</h4>
                            <p class="text-sm text-gray-600 mb-3">Optimized for scanned book pages</p>
                            <div class="text-xs text-gray-500">
                                Left: 8%, Top: 5%<br>
                                Right: 92%, Bottom: 95%
                            </div>
                        </div>
                    </div>
                    
                    <!-- Business Card -->
                    <div class="preset-card bg-gray-50 border border-gray-200 rounded-lg p-6 cursor-pointer hover:bg-gray-100 transition-colors" 
                         onclick="applyPreset('card')">
                        <div class="text-center">
                            <i class="fas fa-id-card text-3xl text-red-600 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Business Card</h4>
                            <p class="text-sm text-gray-600 mb-3">Center crop for card-like documents</p>
                            <div class="text-xs text-gray-500">
                                Left: 20%, Top: 25%<br>
                                Right: 80%, Bottom: 75%
                            </div>
                        </div>
                    </div>
                    
                    <!-- Custom -->
                    <div class="preset-card bg-gray-50 border border-gray-200 rounded-lg p-6 cursor-pointer hover:bg-gray-100 transition-colors" 
                         onclick="switchToManualCrop()">
                        <div class="text-center">
                            <i class="fas fa-cog text-3xl text-gray-600 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Custom Crop</h4>
                            <p class="text-sm text-gray-600 mb-3">Set your own crop dimensions</p>
                            <div class="text-xs text-gray-500">
                                Switch to Manual Crop tab<br>
                                for custom settings
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewCropBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Crop
            </button>
            <button id="applyCropBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-crop-alt mr-2"></i>Apply Crop
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-yellow-200 border-t-yellow-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Cropping your PDF pages...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully cropped!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newCropBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Crop Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-yellow-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-yellow-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-yellow-700">
            <li>Upload your PDF file that needs cropping</li>
            <li>Choose crop method: Manual, Auto, or Preset</li>
            <li>For manual crop, set margins using percentages or pixels</li>
            <li>For auto crop, adjust sensitivity for margin detection</li>
            <li>For presets, select from common crop patterns</li>
            <li>Preview your crop settings before applying</li>
            <li>Apply crop and download the processed PDF</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> Use Auto Crop for scanned documents with white margins. 
                Use Manual Crop for precise control over crop dimensions. 
                Presets are perfect for common document types.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let currentTab = 'manual';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        updateCropPreview();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Tab switching
        document.getElementById('manualCropTab').addEventListener('click', () => switchTab('manual'));
        document.getElementById('autoCropTab').addEventListener('click', () => switchTab('auto'));
        document.getElementById('presetCropTab').addEventListener('click', () => switchTab('preset'));
        
        // Manual crop inputs
        ['cropLeft', 'cropTop', 'cropRight', 'cropBottom'].forEach(id => {
            document.getElementById(id).addEventListener('input', updateCropPreview);
        });
        
        // Auto crop threshold
        document.getElementById('autoThreshold').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = Math.round(this.value * 100) + '%';
        });
        
        // Page range
        document.querySelectorAll('input[name="pageRange"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const specificPages = document.getElementById('specificPages');
                if (this.value === 'specific') {
                    specificPages.classList.remove('hidden');
                } else {
                    specificPages.classList.add('hidden');
                }
            });
        });
        
        // Buttons
        document.getElementById('previewCropBtn').addEventListener('click', previewCrop);
        document.getElementById('applyCropBtn').addEventListener('click', applyCrop);
        document.getElementById('newCropBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('cropOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function switchTab(tab) {
        // Update tab buttons
        document.querySelectorAll('[id$="CropTab"]').forEach(btn => {
            btn.classList.remove('bg-white', 'text-yellow-600', 'shadow-sm');
            btn.classList.add('text-gray-600');
        });
        
        document.getElementById(tab + 'CropTab').classList.add('bg-white', 'text-yellow-600', 'shadow-sm');
        document.getElementById(tab + 'CropTab').classList.remove('text-gray-600');
        
        // Update panels
        document.querySelectorAll('.crop-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        document.getElementById(tab + 'CropPanel').classList.remove('hidden');
        currentTab = tab;
    }
    
    function setCropValues(left, top, right, bottom) {
        document.getElementById('cropLeft').value = left;
        document.getElementById('cropTop').value = top;
        document.getElementById('cropRight').value = right;
        document.getElementById('cropBottom').value = bottom;
        updateCropPreview();
    }
    
    function updateCropPreview() {
        const left = parseFloat(document.getElementById('cropLeft').value) || 0;
        const top = parseFloat(document.getElementById('cropTop').value) || 0;
        const right = parseFloat(document.getElementById('cropRight').value) || 100;
        const bottom = parseFloat(document.getElementById('cropBottom').value) || 100;
        
        const overlay = document.getElementById('cropOverlay');
        const preview = document.getElementById('cropPreview');
        
        if (overlay && preview) {
            const previewWidth = 300;
            const previewHeight = 400;
            
            const overlayLeft = (left / 100) * previewWidth;
            const overlayTop = (top / 100) * previewHeight;
            const overlayWidth = ((right - left) / 100) * previewWidth;
            const overlayHeight = ((bottom - top) / 100) * previewHeight;
            
            overlay.style.left = overlayLeft + 'px';
            overlay.style.top = overlayTop + 'px';
            overlay.style.width = overlayWidth + 'px';
            overlay.style.height = overlayHeight + 'px';
        }
    }
    
    function applyPreset(preset) {
        switch (preset) {
            case 'document':
                setCropValues(10, 10, 90, 90);
                break;
            case 'header-footer':
                setCropValues(0, 15, 100, 85);
                break;
            case 'sides':
                setCropValues(15, 0, 85, 100);
                break;
            case 'book':
                setCropValues(8, 5, 92, 95);
                break;
            case 'card':
                setCropValues(20, 25, 80, 75);
                break;
        }
        
        // Switch to manual tab to show the applied values
        switchTab('manual');
    }
    
    function switchToManualCrop() {
        switchTab('manual');
    }
    
    function previewCrop() {
        alert('Preview functionality would show the first page with crop applied.');
    }
    
    function applyCrop() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        if (currentTab === 'manual') {
            const unit = document.querySelector('input[name="cropUnit"]:checked').value;
            formData.append('left', document.getElementById('cropLeft').value);
            formData.append('top', document.getElementById('cropTop').value);
            formData.append('right', document.getElementById('cropRight').value);
            formData.append('bottom', document.getElementById('cropBottom').value);
            formData.append('unit', unit);
            
            const pageRange = document.querySelector('input[name="pageRange"]:checked').value;
            if (pageRange === 'specific') {
                const specificPages = document.getElementById('specificPages').value;
                if (!specificPages.trim()) {
                    alert('Please specify page numbers.');
                    return;
                }
                formData.append('pages', specificPages);
            } else {
                formData.append('pages', 'all');
            }
            
            cropPDF('/api/crop_pdf', formData);
            
        } else if (currentTab === 'auto') {
            const threshold = document.getElementById('autoThreshold').value;
            formData.append('threshold', threshold);
            
            cropPDF('/api/auto_crop_pdf', formData);
        }
    }
    
    function cropPDF(endpoint, formData) {
        // Show progress
        document.getElementById('cropOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch(endpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Crop operation failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cropped.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error cropping PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('cropOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('cropOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset crop values
        setCropValues(0, 0, 100, 100);
        document.getElementById('specificPages').classList.add('hidden');
        document.querySelector('input[name="pageRange"][value="all"]').checked = true;
    }
</script>
{% endblock %}
