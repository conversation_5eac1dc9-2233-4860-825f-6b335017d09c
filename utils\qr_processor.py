import os
import tempfile
import base64
from io import BytesIO
from PIL import Image
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.colors import black, white
import qrcode
import json

class QRProcessor:
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def create_qr_pdf(self, qr_image_data, qr_data, qr_type):
        """Create a PDF with QR code and information"""
        try:
            # Create temporary file for output
            output_path = os.path.join(self.temp_dir, f'qrcode_{os.urandom(8).hex()}.pdf')
            
            # Create PDF canvas
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4
            
            # Process QR image data
            if qr_image_data.startswith('data:image'):
                # Remove data URL prefix
                image_data = qr_image_data.split(',')[1]
                image_bytes = base64.b64decode(image_data)
                
                # Save temporary image
                temp_image_path = os.path.join(self.temp_dir, f'qr_temp_{os.urandom(8).hex()}.png')
                with open(temp_image_path, 'wb') as f:
                    f.write(image_bytes)
                
                # Add QR code image to PDF
                qr_size = 200  # 200 points
                x = (width - qr_size) / 2
                y = height - 150 - qr_size
                
                c.drawImage(temp_image_path, x, y, width=qr_size, height=qr_size)
                
                # Clean up temp image
                os.unlink(temp_image_path)
            
            # Add title
            c.setFont("Helvetica-Bold", 24)
            c.drawCentredText(width/2, height - 80, "QR Code")
            
            # Add QR type
            c.setFont("Helvetica", 14)
            c.drawCentredText(width/2, height - 110, f"Type: {qr_type.title()}")
            
            # Add QR data information
            c.setFont("Helvetica", 12)
            y_pos = height - 400
            
            # Split long data into multiple lines
            max_chars = 80
            lines = []
            if len(qr_data) > max_chars:
                for i in range(0, len(qr_data), max_chars):
                    lines.append(qr_data[i:i+max_chars])
            else:
                lines = [qr_data]
            
            c.drawCentredText(width/2, y_pos, "QR Code Content:")
            y_pos -= 20
            
            for line in lines[:10]:  # Limit to 10 lines
                c.drawCentredText(width/2, y_pos, line)
                y_pos -= 15
            
            if len(lines) > 10:
                c.drawCentredText(width/2, y_pos, "... (content truncated)")
            
            # Add instructions
            y_pos -= 40
            c.setFont("Helvetica-Oblique", 10)
            instructions = [
                "Instructions:",
                "1. Use any QR code scanner app on your smartphone",
                "2. Point the camera at the QR code above",
                "3. Follow the action suggested by your device",
                "",
                f"Generated on: {self.get_current_datetime()}"
            ]
            
            for instruction in instructions:
                c.drawCentredText(width/2, y_pos, instruction)
                y_pos -= 12
            
            # Add footer
            c.setFont("Helvetica", 8)
            c.drawCentredText(width/2, 50, "Generated by PDF2Any - QR Code Generator")
            
            c.save()
            return output_path
            
        except Exception as e:
            raise Exception(f"Error creating QR PDF: {str(e)}")
    
    def generate_qr_code(self, data, size=300, error_correction='M', 
                        foreground_color='#000000', background_color='#ffffff'):
        """Generate QR code image"""
        try:
            # Set error correction level
            error_levels = {
                'L': qrcode.constants.ERROR_CORRECT_L,
                'M': qrcode.constants.ERROR_CORRECT_M,
                'Q': qrcode.constants.ERROR_CORRECT_Q,
                'H': qrcode.constants.ERROR_CORRECT_H
            }
            
            qr = qrcode.QRCode(
                version=1,
                error_correction=error_levels.get(error_correction, qrcode.constants.ERROR_CORRECT_M),
                box_size=10,
                border=4,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # Create QR code image
            img = qr.make_image(
                fill_color=foreground_color,
                back_color=background_color
            )
            
            # Resize to specified size
            img = img.resize((size, size), Image.Resampling.LANCZOS)
            
            # Convert to base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_data = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{img_data}"
            
        except Exception as e:
            raise Exception(f"Error generating QR code: {str(e)}")
    
    def create_vcard(self, contact_info):
        """Create vCard format string"""
        vcard = "BEGIN:VCARD\nVERSION:3.0\n"
        
        if contact_info.get('first_name') or contact_info.get('last_name'):
            fn = f"{contact_info.get('first_name', '')} {contact_info.get('last_name', '')}".strip()
            vcard += f"FN:{fn}\n"
            vcard += f"N:{contact_info.get('last_name', '')};{contact_info.get('first_name', '')};;;\n"
        
        if contact_info.get('organization'):
            vcard += f"ORG:{contact_info['organization']}\n"
        
        if contact_info.get('phone'):
            vcard += f"TEL:{contact_info['phone']}\n"
        
        if contact_info.get('email'):
            vcard += f"EMAIL:{contact_info['email']}\n"
        
        if contact_info.get('url'):
            vcard += f"URL:{contact_info['url']}\n"
        
        vcard += "END:VCARD"
        return vcard
    
    def create_wifi_string(self, ssid, password, security='WPA'):
        """Create WiFi QR code string"""
        return f"WIFI:T:{security};S:{ssid};P:{password};H:false;;"
    
    def create_email_string(self, email, subject='', body=''):
        """Create email QR code string"""
        email_str = f"mailto:{email}"
        params = []
        
        if subject:
            params.append(f"subject={subject}")
        if body:
            params.append(f"body={body}")
        
        if params:
            email_str += "?" + "&".join(params)
        
        return email_str
    
    def create_sms_string(self, phone, message=''):
        """Create SMS QR code string"""
        sms_str = f"sms:{phone}"
        if message:
            sms_str += f"?body={message}"
        return sms_str
    
    def create_location_string(self, latitude, longitude):
        """Create location QR code string"""
        return f"geo:{latitude},{longitude}"
    
    def get_current_datetime(self):
        """Get current date and time formatted"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def validate_qr_data(self, data, qr_type):
        """Validate QR code data based on type"""
        if not data:
            return False, "Data cannot be empty"
        
        if qr_type == 'url':
            if not (data.startswith('http://') or data.startswith('https://')):
                return False, "URL must start with http:// or https://"
        
        elif qr_type == 'email':
            if '@' not in data:
                return False, "Invalid email format"
        
        elif qr_type == 'phone':
            # Basic phone validation
            cleaned = ''.join(filter(str.isdigit, data))
            if len(cleaned) < 7:
                return False, "Phone number too short"
        
        elif qr_type == 'location':
            try:
                parts = data.replace('geo:', '').split(',')
                if len(parts) != 2:
                    return False, "Location must have latitude and longitude"
                float(parts[0])  # Validate latitude
                float(parts[1])  # Validate longitude
            except ValueError:
                return False, "Invalid location coordinates"
        
        # Check data length (QR codes have limits)
        if len(data) > 2000:
            return False, "Data too long for QR code (max 2000 characters)"
        
        return True, "Valid"
    
    def get_qr_info(self, data):
        """Get information about QR code capacity and recommendations"""
        info = {
            'data_length': len(data),
            'estimated_size': 'Small' if len(data) < 100 else 'Medium' if len(data) < 500 else 'Large',
            'recommended_error_correction': 'L' if len(data) > 1000 else 'M' if len(data) > 500 else 'H',
            'max_capacity': {
                'numeric': 7089,
                'alphanumeric': 4296,
                'binary': 2953,
                'kanji': 1817
            }
        }
        
        return info
