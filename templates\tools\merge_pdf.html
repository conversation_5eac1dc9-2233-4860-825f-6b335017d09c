{% extends "base.html" %}

{% block title %}{{ t.tools_list.merge_pdf.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-layer-group text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.merge_pdf.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.merge_pdf.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select multiple PDF files to merge</p>
            </div>
            
            <input type="file" id="fileInput" multiple accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File List -->
        <div id="fileList" class="mt-8 hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Selected Files:</h3>
            <div id="fileItems" class="space-y-3"></div>
            
            <!-- Merge Options -->
            <div class="mt-8 p-6 bg-gray-50 rounded-lg">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Merge Options:</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Output Filename:</label>
                        <input type="text" id="outputName" value="merged_document" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Page Order:</label>
                        <select id="pageOrder" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="original">Original Order</option>
                            <option value="alphabetical">Alphabetical</option>
                            <option value="reverse">Reverse Order</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex justify-center space-x-4 mt-8">
                <button id="clearBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Clear All
                </button>
                <button id="mergeBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-layer-group mr-2"></i>
                    Merge PDFs
                </button>
            </div>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Merging your PDF files...</p>
            
            <div class="w-full bg-gray-200 rounded-full h-2 mt-6">
                <div id="progressBar" class="progress-bar h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF files have been successfully merged!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>
                    {{ t.upload.download }}
                </button>
                <button id="newMergeBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Merge More Files
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-blue-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-blue-700">
            <li>Select multiple PDF files by clicking "Select Files" or drag and drop them</li>
            <li>Arrange the files in your desired order by dragging them</li>
            <li>Choose merge options like output filename and page order</li>
            <li>Click "Merge PDFs" to combine all files into one document</li>
            <li>Download your merged PDF file</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFiles = [];
    let dragCounter = 0;
    
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    const fileItems = document.getElementById('fileItems');
    const progressSection = document.getElementById('progressSection');
    const resultSection = document.getElementById('resultSection');
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragenter', function(e) {
        e.preventDefault();
        dragCounter++;
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dragCounter--;
        if (dragCounter === 0) {
            this.classList.remove('dragover');
        }
    });
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dragCounter = 0;
        this.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files).filter(file => file.type === 'application/pdf');
        handleFiles(files);
    });
    
    // File input change
    fileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        handleFiles(files);
    });
    
    function handleFiles(files) {
        selectedFiles = [...selectedFiles, ...files];
        displayFiles();
        fileList.classList.remove('hidden');
    }
    
    function displayFiles() {
        fileItems.innerHTML = '';
        selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'flex items-center justify-between p-4 bg-gray-50 rounded-lg';
            fileItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">${file.name}</p>
                        <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                </div>
                <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            `;
            fileItems.appendChild(fileItem);
        });
    }
    
    function removeFile(index) {
        selectedFiles.splice(index, 1);
        displayFiles();
        if (selectedFiles.length === 0) {
            fileList.classList.add('hidden');
        }
    }
    
    // Clear all files
    document.getElementById('clearBtn').addEventListener('click', function() {
        selectedFiles = [];
        fileList.classList.add('hidden');
        fileInput.value = '';
    });
    
    // Merge PDFs
    document.getElementById('mergeBtn').addEventListener('click', function() {
        if (selectedFiles.length < 2) {
            alert('Please select at least 2 PDF files to merge.');
            return;
        }
        
        mergePDFs();
    });
    
    function mergePDFs() {
        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append('files', file);
        });
        
        formData.append('output_name', document.getElementById('outputName').value);
        formData.append('page_order', document.getElementById('pageOrder').value);
        
        // Show progress
        fileList.classList.add('hidden');
        progressSection.classList.remove('hidden');
        
        // Simulate progress
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);
        
        fetch('/api/merge_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Merge failed');
            }
        })
        .then(blob => {
            setTimeout(() => {
                progressSection.classList.add('hidden');
                resultSection.classList.remove('hidden');
                
                // Setup download
                const url = window.URL.createObjectURL(blob);
                document.getElementById('downloadBtn').onclick = function() {
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = document.getElementById('outputName').value + '.pdf';
                    a.click();
                };
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            alert('Error merging PDFs: ' + error.message);
            progressSection.classList.add('hidden');
            fileList.classList.remove('hidden');
        });
    }
    
    // New merge button
    document.getElementById('newMergeBtn').addEventListener('click', function() {
        selectedFiles = [];
        fileInput.value = '';
        resultSection.classList.add('hidden');
        fileList.classList.add('hidden');
    });
</script>
{% endblock %}
