{% extends "base.html" %}

{% block title %}PDF to GIF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-file-image text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">PDF to GIF Converter</h1>
        <p class="text-xl text-gray-600">Convert PDF pages to animated GIF</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to convert to animated GIF</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                Select PDF File
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Conversion Options -->
    <div id="conversionOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">GIF Animation Settings</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Animation Settings -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Animation Options</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Frame Duration (ms)</label>
                        <input type="range" id="duration" min="100" max="3000" value="500" class="w-full">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Fast (100ms)</span>
                            <span id="durationValue">500ms</span>
                            <span>Slow (3000ms)</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">How long each page is displayed</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Loop Count</label>
                        <select id="loop" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="0" selected>Infinite Loop</option>
                            <option value="1">Play Once</option>
                            <option value="2">Play Twice</option>
                            <option value="3">Play 3 Times</option>
                            <option value="5">Play 5 Times</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Image Quality</label>
                        <select id="quality" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="95">High Quality (95%)</option>
                            <option value="85" selected>Good Quality (85%)</option>
                            <option value="75">Standard Quality (75%)</option>
                            <option value="60">Low Quality (60%)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Width (px)</label>
                        <select id="maxWidth" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="400">Small (400px)</option>
                            <option value="600">Medium (600px)</option>
                            <option value="800" selected>Large (800px)</option>
                            <option value="1000">Extra Large (1000px)</option>
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Larger sizes create bigger files</p>
                    </div>
                </div>
            </div>
            
            <!-- Preview & Info -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Preview & Information</h3>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h4 class="font-medium text-gray-800 mb-2">Animation Preview</h4>
                    <div class="text-center">
                        <div class="inline-block bg-white border-2 border-gray-300 rounded p-4">
                            <i class="fas fa-file-pdf text-4xl text-red-500 mb-2"></i>
                            <p class="text-sm text-gray-600">PDF Pages</p>
                            <div class="flex justify-center mt-2">
                                <i class="fas fa-arrow-right text-gray-400 mx-2"></i>
                            </div>
                        </div>
                        <div class="inline-block bg-white border-2 border-gray-300 rounded p-4 ml-4">
                            <i class="fas fa-file-image text-4xl text-green-500 mb-2"></i>
                            <p class="text-sm text-gray-600">Animated GIF</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-medium text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        Estimated Output
                    </h4>
                    <div class="text-sm text-blue-700 space-y-1">
                        <p><strong>Duration per frame:</strong> <span id="previewDuration">500ms</span></p>
                        <p><strong>Loop behavior:</strong> <span id="previewLoop">Infinite</span></p>
                        <p><strong>Max dimensions:</strong> <span id="previewSize">800px width</span></p>
                        <p><strong>Quality:</strong> <span id="previewQuality">Good (85%)</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Advanced Options -->
        <div class="mt-8 p-6 bg-gray-50 rounded-lg">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Advanced Options:</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="optimizeColors" checked class="mr-2">
                    Optimize colors for smaller file size
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="maintainAspect" checked class="mr-2">
                    Maintain aspect ratio
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="addPageNumbers" class="mr-2">
                    Show page numbers in animation
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="reverseOrder" class="mr-2">
                    Reverse page order
                </label>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Settings
            </button>
            <button id="convertBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-file-image mr-2"></i>Create GIF Animation
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Converting PDF pages to animated GIF...</p>
            
            <div class="w-full bg-gray-200 rounded-full h-2 mt-6">
                <div id="progressBar" class="bg-gradient-to-r from-green-500 to-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            
            <div id="progressText" class="mt-4 text-sm text-gray-600">
                Processing pages...
            </div>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully converted to an animated GIF!</p>
            
            <!-- GIF Preview -->
            <div id="gifPreview" class="mb-6">
                <!-- GIF preview will be shown here -->
            </div>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newConversionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Convert Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-green-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-green-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-green-700">
            <li>Upload your PDF file (works best with 2-20 pages)</li>
            <li>Set the frame duration (how long each page is shown)</li>
            <li>Choose loop behavior (infinite or specific number of loops)</li>
            <li>Select image quality and maximum width</li>
            <li>Configure advanced options if needed</li>
            <li>Create your animated GIF and download it</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tips:</strong> 
                • Use shorter durations (100-300ms) for flipbook effects
                • Use longer durations (1000-2000ms) for presentations
                • Smaller max width creates smaller file sizes
                • PDFs with many pages will create large GIF files
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        updatePreview();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Settings change listeners
        document.getElementById('duration').addEventListener('input', function() {
            document.getElementById('durationValue').textContent = this.value + 'ms';
            updatePreview();
        });
        
        document.getElementById('loop').addEventListener('change', updatePreview);
        document.getElementById('quality').addEventListener('change', updatePreview);
        document.getElementById('maxWidth').addEventListener('change', updatePreview);
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewSettings);
        document.getElementById('convertBtn').addEventListener('click', convertToGIF);
        document.getElementById('newConversionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('conversionOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function updatePreview() {
        const duration = document.getElementById('duration').value;
        const loop = document.getElementById('loop').value;
        const quality = document.getElementById('quality').value;
        const maxWidth = document.getElementById('maxWidth').value;
        
        document.getElementById('previewDuration').textContent = duration + 'ms';
        
        const loopText = loop === '0' ? 'Infinite' : `${loop} time${loop > 1 ? 's' : ''}`;
        document.getElementById('previewLoop').textContent = loopText;
        
        document.getElementById('previewSize').textContent = maxWidth + 'px width';
        
        const qualityText = {
            '95': 'High (95%)',
            '85': 'Good (85%)',
            '75': 'Standard (75%)',
            '60': 'Low (60%)'
        };
        document.getElementById('previewQuality').textContent = qualityText[quality];
    }
    
    function previewSettings() {
        const duration = document.getElementById('duration').value;
        const loop = document.getElementById('loop').value;
        const quality = document.getElementById('quality').value;
        const maxWidth = document.getElementById('maxWidth').value;
        
        const loopText = loop === '0' ? 'infinite loop' : `${loop} time${loop > 1 ? 's' : ''}`;
        
        alert(`Preview Settings:
        
• Frame Duration: ${duration}ms per page
• Loop Behavior: ${loopText}
• Image Quality: ${quality}%
• Maximum Width: ${maxWidth}px
• Color Optimization: ${document.getElementById('optimizeColors').checked ? 'Enabled' : 'Disabled'}
• Aspect Ratio: ${document.getElementById('maintainAspect').checked ? 'Maintained' : 'Not maintained'}

The GIF will cycle through each PDF page at the specified duration.`);
    }
    
    function convertToGIF() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('duration', document.getElementById('duration').value);
        formData.append('loop', document.getElementById('loop').value);
        formData.append('quality', document.getElementById('quality').value);
        formData.append('max_width', document.getElementById('maxWidth').value);
        
        // Show progress
        document.getElementById('conversionOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        // Simulate progress with different stages
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        const stages = [
            'Analyzing PDF pages...',
            'Converting pages to images...',
            'Optimizing colors...',
            'Creating GIF animation...',
            'Finalizing output...'
        ];
        
        let currentStage = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
            
            if (progress > (currentStage + 1) * 18 && currentStage < stages.length - 1) {
                currentStage++;
                progressText.textContent = stages[currentStage];
            }
        }, 300);
        
        fetch('/api/pdf_to_gif', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            progressText.textContent = 'Complete!';
            
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Conversion failed');
            }
        })
        .then(blob => {
            setTimeout(() => {
                document.getElementById('progressSection').classList.add('hidden');
                document.getElementById('resultSection').classList.remove('hidden');
                
                // Show GIF preview
                const url = window.URL.createObjectURL(blob);
                document.getElementById('gifPreview').innerHTML = `
                    <div class="inline-block border-2 border-gray-300 rounded-lg p-4 bg-white">
                        <img src="${url}" alt="Generated GIF" class="max-w-xs max-h-48 mx-auto rounded">
                        <p class="text-sm text-gray-600 mt-2">Your animated GIF</p>
                    </div>
                `;
                
                // Setup download
                document.getElementById('downloadBtn').onclick = function() {
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'pdf_animation.gif';
                    a.click();
                };
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            alert('Error converting PDF to GIF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('conversionOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('conversionOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset form values
        document.getElementById('duration').value = 500;
        document.getElementById('durationValue').textContent = '500ms';
        document.getElementById('loop').value = '0';
        document.getElementById('quality').value = '85';
        document.getElementById('maxWidth').value = '800';
        
        // Reset checkboxes
        document.getElementById('optimizeColors').checked = true;
        document.getElementById('maintainAspect').checked = true;
        document.getElementById('addPageNumbers').checked = false;
        document.getElementById('reverseOrder').checked = false;
        
        updatePreview();
    }
</script>
{% endblock %}
