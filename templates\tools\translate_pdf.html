{% extends "base.html" %}

{% block title %}{{ t.tools_list.translate_pdf.name }} - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-language text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools_list.translate_pdf.name }}</h1>
        <p class="text-xl text-gray-600">{{ t.tools_list.translate_pdf.description }}</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to extract and translate text</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Translation Options -->
    <div id="translationOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Translation Options</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Target Language</label>
                <select id="targetLanguage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="en">English</option>
                    <option value="ar">العربية (Arabic)</option>
                    <option value="es">Español (Spanish)</option>
                    <option value="fr">Français (French)</option>
                    <option value="de">Deutsch (German)</option>
                    <option value="it">Italiano (Italian)</option>
                    <option value="pt">Português (Portuguese)</option>
                    <option value="ru">Русский (Russian)</option>
                    <option value="ja">日本語 (Japanese)</option>
                    <option value="ko">한국어 (Korean)</option>
                    <option value="zh">中文 (Chinese)</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Translation Method</label>
                <select id="translationMethod" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="google">Google Translate</option>
                    <option value="html">HTML Interface</option>
                </select>
            </div>
        </div>
        
        <div class="flex justify-center space-x-4">
            <button id="extractBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-language mr-2"></i>Extract & Prepare Translation
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Extracting text from your PDF...</p>
        </div>
    </div>
    
    <!-- Translation Results -->
    <div id="translationResults" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">Translation Results</h2>
            <div class="flex space-x-2">
                <button id="downloadHtmlBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors hidden">
                    <i class="fas fa-download mr-2"></i>Download HTML
                </button>
                <button id="newTranslationBtn" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    New Translation
                </button>
            </div>
        </div>
        
        <!-- Summary Stats -->
        <div id="translationSummary" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <!-- Stats will be populated here -->
        </div>
        
        <!-- Translation Content -->
        <div id="translationContent" class="space-y-6">
            <!-- Translation pages will be populated here -->
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-indigo-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-indigo-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-indigo-700">
            <li>Upload your PDF file containing text you want to translate</li>
            <li>Select your target language from the dropdown</li>
            <li>Choose translation method (Google Translate or HTML interface)</li>
            <li>Click "Extract & Prepare Translation" to process the PDF</li>
            <li>Use the provided Google Translate links to translate text chunks</li>
            <li>Download the HTML interface for offline translation work</li>
        </ol>
        
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-yellow-800 text-sm">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <strong>Note:</strong> This tool extracts text from PDFs and provides Google Translate links. 
                For best results, ensure your PDF contains selectable text (not scanned images).
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let translationData = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Extract button
        document.getElementById('extractBtn').addEventListener('click', extractAndTranslate);
        
        // New translation button
        document.getElementById('newTranslationBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('translationOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function extractAndTranslate() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('target_lang', document.getElementById('targetLanguage').value);
        
        // Show progress
        document.getElementById('translationOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/translate_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            translationData = data;
            displayTranslationResults(data);
            
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('translationResults').classList.remove('hidden');
        })
        .catch(error => {
            alert('Error processing PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('translationOptions').classList.remove('hidden');
        });
    }
    
    function displayTranslationResults(data) {
        // Display summary
        const summary = document.getElementById('translationSummary');
        const totalWords = data.pages.reduce((sum, page) => sum + page.word_count, 0);
        const totalChars = data.pages.reduce((sum, page) => sum + page.char_count, 0);
        const languages = [...new Set(data.pages.map(page => page.detected_language))];
        
        summary.innerHTML = `
            <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">${data.total_pages}</div>
                <div class="text-gray-600 text-sm">Pages</div>
            </div>
            <div class="bg-green-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600">${totalWords}</div>
                <div class="text-gray-600 text-sm">Words</div>
            </div>
            <div class="bg-purple-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-purple-600">${totalChars}</div>
                <div class="text-gray-600 text-sm">Characters</div>
            </div>
            <div class="bg-orange-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-orange-600">${languages.join(', ').toUpperCase()}</div>
                <div class="text-gray-600 text-sm">Detected</div>
            </div>
        `;
        
        // Display pages
        const content = document.getElementById('translationContent');
        content.innerHTML = '';
        
        data.pages.forEach((page, index) => {
            const pageDiv = document.createElement('div');
            pageDiv.className = 'border border-gray-200 rounded-lg p-6';
            
            const langClass = page.detected_language === 'ar' ? 'text-right' : 'text-left';
            const direction = page.detected_language === 'ar' ? 'rtl' : 'ltr';
            
            pageDiv.innerHTML = `
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">
                        Page ${page.page_number}
                        <span class="text-sm text-gray-500 ml-2">
                            (${page.detected_language.toUpperCase()}) - ${page.word_count} words
                        </span>
                    </h3>
                    <a href="${page.translate_url}" target="_blank" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        <i class="fas fa-external-link-alt mr-1"></i>
                        Translate Full Page
                    </a>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-4" dir="${direction}">
                    <h4 class="font-medium text-gray-700 mb-2">Original Text:</h4>
                    <div class="text-gray-800 text-sm leading-relaxed ${langClass} whitespace-pre-wrap max-h-40 overflow-y-auto">
                        ${page.original_text}
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    ${page.text_chunks.map((chunk, i) => `
                        <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-xs font-medium text-gray-600">Chunk ${i + 1}</span>
                                <a href="${createChunkTranslateUrl(chunk, page.detected_language, data.target_language)}" 
                                   target="_blank" 
                                   class="text-blue-600 hover:text-blue-800 text-xs">
                                    <i class="fas fa-language mr-1"></i>Translate
                                </a>
                            </div>
                            <div class="text-xs text-gray-700 ${langClass}" dir="${direction}">
                                ${chunk.length > 100 ? chunk.substring(0, 100) + '...' : chunk}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            content.appendChild(pageDiv);
        });
        
        // Show download HTML button if method is HTML
        if (document.getElementById('translationMethod').value === 'html') {
            document.getElementById('downloadHtmlBtn').classList.remove('hidden');
            document.getElementById('downloadHtmlBtn').onclick = downloadHtmlInterface;
        }
    }
    
    function createChunkTranslateUrl(text, sourceLang, targetLang) {
        const encodedText = encodeURIComponent(text.substring(0, 5000)); // Limit for URL
        return `https://translate.google.com/?sl=${sourceLang}&tl=${targetLang}&text=${encodedText}`;
    }
    
    function downloadHtmlInterface() {
        if (!translationData) return;
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('target_lang', document.getElementById('targetLanguage').value);
        
        fetch('/api/create_translation_html', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Failed to create HTML interface');
            }
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'translation_interface.html';
            a.click();
            window.URL.revokeObjectURL(url);
        })
        .catch(error => {
            alert('Error creating HTML interface: ' + error.message);
        });
    }
    
    function resetForm() {
        selectedFile = null;
        translationData = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('translationOptions').classList.add('hidden');
        document.getElementById('translationResults').classList.add('hidden');
        document.getElementById('downloadHtmlBtn').classList.add('hidden');
    }
</script>
{% endblock %}
