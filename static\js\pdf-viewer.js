/**
 * Interactive PDF Viewer Component
 * Uses PDF.js for client-side PDF rendering and manipulation
 */

class PDFViewer {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            showThumbnails: options.showThumbnails !== false,
            allowReorder: options.allowReorder || false,
            allowSelection: options.allowSelection || false,
            allowCrop: options.allowCrop || false,
            allowAnnotation: options.allowAnnotation || false,
            zoomLevels: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2, 3, 4],
            defaultZoom: 1,
            ...options
        };
        
        this.pdfDoc = null;
        this.currentPage = 1;
        this.totalPages = 0;
        this.currentZoom = this.options.defaultZoom;
        this.pages = [];
        this.selectedAreas = [];
        this.annotations = [];
        
        this.init();
    }
    
    init() {
        this.createViewerHTML();
        this.setupEventListeners();
        
        // Load PDF.js
        if (typeof pdfjsLib === 'undefined') {
            this.loadPDFJS().then(() => {
                this.setupPDFJS();
            });
        } else {
            this.setupPDFJS();
        }
    }
    
    async loadPDFJS() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    setupPDFJS() {
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
    }
    
    createViewerHTML() {
        this.container.innerHTML = `
            <div class="pdf-viewer">
                <!-- Toolbar -->
                <div class="pdf-toolbar bg-gray-100 border-b border-gray-300 p-3 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button id="prevPage" class="btn-secondary px-3 py-1 rounded" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="text-sm">
                            Page <input type="number" id="currentPageInput" value="1" min="1" class="w-12 text-center border rounded px-1"> 
                            of <span id="totalPages">0</span>
                        </span>
                        <button id="nextPage" class="btn-secondary px-3 py-1 rounded" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button id="zoomOut" class="btn-secondary px-3 py-1 rounded">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <select id="zoomSelect" class="border rounded px-2 py-1">
                            <option value="0.25">25%</option>
                            <option value="0.5">50%</option>
                            <option value="0.75">75%</option>
                            <option value="1" selected>100%</option>
                            <option value="1.25">125%</option>
                            <option value="1.5">150%</option>
                            <option value="2">200%</option>
                            <option value="3">300%</option>
                            <option value="4">400%</option>
                        </select>
                        <button id="zoomIn" class="btn-secondary px-3 py-1 rounded">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        ${this.options.showThumbnails ? '<button id="toggleThumbnails" class="btn-secondary px-3 py-1 rounded"><i class="fas fa-th-large"></i></button>' : ''}
                        ${this.options.allowSelection ? '<button id="toggleSelection" class="btn-secondary px-3 py-1 rounded"><i class="fas fa-mouse-pointer"></i></button>' : ''}
                        ${this.options.allowCrop ? '<button id="toggleCrop" class="btn-secondary px-3 py-1 rounded"><i class="fas fa-crop"></i></button>' : ''}
                        ${this.options.allowAnnotation ? '<button id="toggleAnnotation" class="btn-secondary px-3 py-1 rounded"><i class="fas fa-pen"></i></button>' : ''}
                    </div>
                </div>
                
                <!-- Main Content -->
                <div class="pdf-content flex">
                    <!-- Thumbnails Panel -->
                    ${this.options.showThumbnails ? `
                    <div id="thumbnailsPanel" class="thumbnails-panel w-48 bg-gray-50 border-r border-gray-300 overflow-y-auto">
                        <div class="p-2">
                            <h3 class="text-sm font-semibold text-gray-700 mb-2">Pages</h3>
                            <div id="thumbnailsContainer" class="space-y-2"></div>
                        </div>
                    </div>
                    ` : ''}
                    
                    <!-- Main Viewer -->
                    <div class="main-viewer flex-1 overflow-auto bg-gray-200">
                        <div id="pdfContainer" class="pdf-container p-4">
                            <div id="loadingIndicator" class="text-center py-8">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <p class="mt-2 text-gray-600">Loading PDF...</p>
                            </div>
                            <canvas id="pdfCanvas" class="mx-auto shadow-lg bg-white" style="display: none;"></canvas>
                            <div id="selectionOverlay" class="absolute pointer-events-none" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    setupEventListeners() {
        // Navigation
        document.getElementById('prevPage')?.addEventListener('click', () => this.previousPage());
        document.getElementById('nextPage')?.addEventListener('click', () => this.nextPage());
        document.getElementById('currentPageInput')?.addEventListener('change', (e) => {
            this.goToPage(parseInt(e.target.value));
        });
        
        // Zoom
        document.getElementById('zoomOut')?.addEventListener('click', () => this.zoomOut());
        document.getElementById('zoomIn')?.addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomSelect')?.addEventListener('change', (e) => {
            this.setZoom(parseFloat(e.target.value));
        });
        
        // Tool toggles
        document.getElementById('toggleThumbnails')?.addEventListener('click', () => this.toggleThumbnails());
        document.getElementById('toggleSelection')?.addEventListener('click', () => this.toggleSelectionMode());
        document.getElementById('toggleCrop')?.addEventListener('click', () => this.toggleCropMode());
        document.getElementById('toggleAnnotation')?.addEventListener('click', () => this.toggleAnnotationMode());
        
        // Canvas interactions
        const canvas = document.getElementById('pdfCanvas');
        if (canvas) {
            canvas.addEventListener('mousedown', (e) => this.handleCanvasMouseDown(e));
            canvas.addEventListener('mousemove', (e) => this.handleCanvasMouseMove(e));
            canvas.addEventListener('mouseup', (e) => this.handleCanvasMouseUp(e));
        }
    }
    
    async loadPDF(file) {
        try {
            this.showLoading(true);
            
            let pdfData;
            if (file instanceof File) {
                pdfData = await file.arrayBuffer();
            } else if (typeof file === 'string') {
                // URL
                pdfData = file;
            } else {
                throw new Error('Invalid file type');
            }
            
            this.pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
            this.totalPages = this.pdfDoc.numPages;
            
            // Update UI
            document.getElementById('totalPages').textContent = this.totalPages;
            document.getElementById('currentPageInput').max = this.totalPages;
            
            // Load first page
            await this.renderPage(1);
            
            // Generate thumbnails if enabled
            if (this.options.showThumbnails) {
                await this.generateThumbnails();
            }
            
            this.showLoading(false);
            this.updateNavigation();
            
            // Trigger loaded event
            this.container.dispatchEvent(new CustomEvent('pdfLoaded', {
                detail: { totalPages: this.totalPages }
            }));
            
        } catch (error) {
            console.error('Error loading PDF:', error);
            this.showError('Failed to load PDF: ' + error.message);
        }
    }
    
    async renderPage(pageNum) {
        if (!this.pdfDoc || pageNum < 1 || pageNum > this.totalPages) return;
        
        try {
            const page = await this.pdfDoc.getPage(pageNum);
            const canvas = document.getElementById('pdfCanvas');
            const ctx = canvas.getContext('2d');
            
            // Calculate viewport
            const viewport = page.getViewport({ scale: this.currentZoom });
            
            // Set canvas dimensions
            canvas.width = viewport.width;
            canvas.height = viewport.height;
            canvas.style.display = 'block';
            
            // Render page
            const renderContext = {
                canvasContext: ctx,
                viewport: viewport
            };
            
            await page.render(renderContext).promise;
            
            this.currentPage = pageNum;
            document.getElementById('currentPageInput').value = pageNum;
            
            // Update page data
            this.pages[pageNum - 1] = {
                pageNum,
                viewport,
                canvas: canvas.cloneNode(),
                rendered: true
            };
            
            // Clear previous selections/annotations for this page
            this.clearPageOverlays();
            
            // Trigger page rendered event
            this.container.dispatchEvent(new CustomEvent('pageRendered', {
                detail: { pageNum, viewport }
            }));
            
        } catch (error) {
            console.error('Error rendering page:', error);
            this.showError('Failed to render page: ' + error.message);
        }
    }
    
    async generateThumbnails() {
        const container = document.getElementById('thumbnailsContainer');
        if (!container) return;
        
        container.innerHTML = '';
        
        for (let i = 1; i <= this.totalPages; i++) {
            const thumbnailDiv = document.createElement('div');
            thumbnailDiv.className = `thumbnail-item p-2 border rounded cursor-pointer hover:bg-blue-50 ${i === this.currentPage ? 'bg-blue-100 border-blue-500' : 'border-gray-300'}`;
            thumbnailDiv.dataset.pageNum = i;
            
            const canvas = document.createElement('canvas');
            canvas.className = 'w-full h-auto';
            
            try {
                const page = await this.pdfDoc.getPage(i);
                const viewport = page.getViewport({ scale: 0.2 });
                
                canvas.width = viewport.width;
                canvas.height = viewport.height;
                
                const ctx = canvas.getContext('2d');
                await page.render({ canvasContext: ctx, viewport }).promise;
                
                thumbnailDiv.appendChild(canvas);
                
                const pageLabel = document.createElement('div');
                pageLabel.className = 'text-xs text-center mt-1 text-gray-600';
                pageLabel.textContent = `Page ${i}`;
                thumbnailDiv.appendChild(pageLabel);
                
                // Add click handler
                thumbnailDiv.addEventListener('click', () => {
                    this.goToPage(i);
                    this.updateThumbnailSelection(i);
                });
                
                container.appendChild(thumbnailDiv);
                
            } catch (error) {
                console.error(`Error generating thumbnail for page ${i}:`, error);
            }
        }
    }
    
    updateThumbnailSelection(pageNum) {
        const thumbnails = document.querySelectorAll('.thumbnail-item');
        thumbnails.forEach(thumb => {
            if (parseInt(thumb.dataset.pageNum) === pageNum) {
                thumb.classList.add('bg-blue-100', 'border-blue-500');
                thumb.classList.remove('border-gray-300');
            } else {
                thumb.classList.remove('bg-blue-100', 'border-blue-500');
                thumb.classList.add('border-gray-300');
            }
        });
    }
    
    // Navigation methods
    previousPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }
    
    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }
    
    async goToPage(pageNum) {
        if (pageNum >= 1 && pageNum <= this.totalPages) {
            await this.renderPage(pageNum);
            this.updateNavigation();
            if (this.options.showThumbnails) {
                this.updateThumbnailSelection(pageNum);
            }
        }
    }
    
    updateNavigation() {
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');
        
        if (prevBtn) prevBtn.disabled = this.currentPage <= 1;
        if (nextBtn) nextBtn.disabled = this.currentPage >= this.totalPages;
    }
    
    // Zoom methods
    zoomIn() {
        const currentIndex = this.options.zoomLevels.indexOf(this.currentZoom);
        if (currentIndex < this.options.zoomLevels.length - 1) {
            this.setZoom(this.options.zoomLevels[currentIndex + 1]);
        }
    }
    
    zoomOut() {
        const currentIndex = this.options.zoomLevels.indexOf(this.currentZoom);
        if (currentIndex > 0) {
            this.setZoom(this.options.zoomLevels[currentIndex - 1]);
        }
    }
    
    async setZoom(zoom) {
        this.currentZoom = zoom;
        document.getElementById('zoomSelect').value = zoom;
        
        // Re-render current page with new zoom
        await this.renderPage(this.currentPage);
        
        // Trigger zoom changed event
        this.container.dispatchEvent(new CustomEvent('zoomChanged', {
            detail: { zoom }
        }));
    }
    
    // Utility methods
    showLoading(show) {
        const loading = document.getElementById('loadingIndicator');
        const canvas = document.getElementById('pdfCanvas');
        
        if (loading) loading.style.display = show ? 'block' : 'none';
        if (canvas) canvas.style.display = show ? 'none' : 'block';
    }
    
    showError(message) {
        const container = document.getElementById('pdfContainer');
        container.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                <p class="text-red-600">${message}</p>
            </div>
        `;
    }
    
    clearPageOverlays() {
        this.selectedAreas = [];
        this.annotations = [];
        const overlay = document.getElementById('selectionOverlay');
        if (overlay) overlay.innerHTML = '';
    }
    
    // Interactive features (to be implemented based on tool needs)
    toggleThumbnails() {
        const panel = document.getElementById('thumbnailsPanel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    toggleSelectionMode() {
        // Implementation for selection mode
        console.log('Selection mode toggled');
    }
    
    toggleCropMode() {
        // Implementation for crop mode
        console.log('Crop mode toggled');
    }
    
    toggleAnnotationMode() {
        // Implementation for annotation mode
        console.log('Annotation mode toggled');
    }
    
    handleCanvasMouseDown(e) {
        // Handle mouse interactions based on current mode
        console.log('Canvas mouse down', e);
    }
    
    handleCanvasMouseMove(e) {
        // Handle mouse move for drawing/selection
        console.log('Canvas mouse move', e);
    }
    
    handleCanvasMouseUp(e) {
        // Handle mouse up
        console.log('Canvas mouse up', e);
    }
    
    // Public API methods
    getCurrentPage() {
        return this.currentPage;
    }
    
    getTotalPages() {
        return this.totalPages;
    }
    
    getCurrentZoom() {
        return this.currentZoom;
    }
    
    getSelectedAreas() {
        return this.selectedAreas;
    }
    
    getAnnotations() {
        return this.annotations;
    }
    
    destroy() {
        // Clean up resources
        if (this.pdfDoc) {
            this.pdfDoc.destroy();
        }
        this.container.innerHTML = '';
    }
}
