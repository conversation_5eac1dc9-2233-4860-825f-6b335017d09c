{% extends "base.html" %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-5xl md:text-6xl font-bold mb-6">
            {{ t.site_name }}
        </h1>
        <p class="text-xl md:text-2xl mb-8 opacity-90">
            {{ t.tagline }}
        </p>
        <div class="flex justify-center">
            <div class="bg-white bg-opacity-20 rounded-full px-8 py-3">
                <p class="text-lg">25 {{ t.tools }} • {{ t.english }} & {{ t.arabic }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Tools Grid -->
<section class="py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">{{ t.tools }}</h2>
            <p class="text-xl text-gray-600">Choose from our comprehensive collection of PDF processing tools</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- Add Page Number -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/add_page_number'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-hashtag text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.add_page_number.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.add_page_number.description }}</p>
                </div>
            </div>
            
            <!-- Merge PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/merge_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-layer-group text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.merge_pdf.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.merge_pdf.description }}</p>
                </div>
            </div>
            
            <!-- Protect PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/protect_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-lock text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.protect_pdf.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.protect_pdf.description }}</p>
                </div>
            </div>
            
            <!-- Compress PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/compress_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-compress-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.compress_pdf.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.compress_pdf.description }}</p>
                </div>
            </div>
            
            <!-- Remove Page -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/remove_page'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-trash-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.remove_page.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.remove_page.description }}</p>
                </div>
            </div>
            
            <!-- Split PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/split_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cut text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.split_pdf.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.split_pdf.description }}</p>
                </div>
            </div>
            
            <!-- Extract Page -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/extract_page'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-export text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.extract_page.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.extract_page.description }}</p>
                </div>
            </div>
            
            <!-- Extract Image -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/extract_image'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-rose-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-images text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.extract_image.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.extract_image.description }}</p>
                </div>
            </div>
        </div>
        
        <!-- Load More Button -->
        <div class="text-center mt-12">
            <button id="loadMoreBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                Load More Tools
            </button>
        </div>
        
        <!-- Hidden Tools (Initially Hidden) -->
        <div id="moreTools" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-6 hidden">
            <!-- Text to PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/text_to_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-gray-500 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.text_to_pdf.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.text_to_pdf.description }}</p>
                </div>
            </div>
            
            <!-- Rotate PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/rotate_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-redo text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.tools_list.rotate_pdf.name }}</h3>
                    <p class="text-gray-600 text-sm">{{ t.tools_list.rotate_pdf.description }}</p>
                </div>
            </div>
            
            <!-- Unlock PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/unlock_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-unlock text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Unlock PDF</h3>
                    <p class="text-gray-600 text-sm">Remove password protection from PDF files</p>
                </div>
            </div>

            <!-- Add Watermark -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/add_watermark'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-tint text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Add Watermark</h3>
                    <p class="text-gray-600 text-sm">Add text, image, or stamp watermarks</p>
                </div>
            </div>

            <!-- PDF to Image -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/pdf_to_image'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-images text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">PDF to Image</h3>
                    <p class="text-gray-600 text-sm">Convert PDF pages to high-quality images</p>
                </div>
            </div>

            <!-- Image to PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/image_to_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-pdf text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Image to PDF</h3>
                    <p class="text-gray-600 text-sm">Convert images to PDF format</p>
                </div>
            </div>

            <!-- Translate PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/translate_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-language text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Translate PDF</h3>
                    <p class="text-gray-600 text-sm">Translate PDF text using Google Translate</p>
                </div>
            </div>

            <!-- Sign PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/sign_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-signature text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Sign PDF</h3>
                    <p class="text-gray-600 text-sm">Add digital signatures to PDF documents</p>
                </div>
            </div>

            <!-- Crop PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/crop_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-crop-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Crop PDF</h3>
                    <p class="text-gray-600 text-sm">Crop PDF pages to remove unwanted areas</p>
                </div>
            </div>

            <!-- PNG to PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/png_to_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-pdf text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">PNG to PDF</h3>
                    <p class="text-gray-600 text-sm">Convert PNG images to PDF format</p>
                </div>
            </div>

            <!-- PDF to PNG -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/pdf_to_png'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-images text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">PDF to PNG</h3>
                    <p class="text-gray-600 text-sm">Convert PDF pages to PNG images</p>
                </div>
            </div>

            <!-- GIF to PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/gif_to_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-pdf text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">GIF to PDF</h3>
                    <p class="text-gray-600 text-sm">Convert animated GIF files to PDF documents</p>
                </div>
            </div>

            <!-- PDF to GIF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/pdf_to_gif'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-image text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">PDF to GIF</h3>
                    <p class="text-gray-600 text-sm">Convert PDF pages to animated GIF</p>
                </div>
            </div>

            <!-- Organize PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/organize_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-sort text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Organize PDF</h3>
                    <p class="text-gray-600 text-sm">Reorder, rotate, and manage PDF pages</p>
                </div>
            </div>

            <!-- PDF to OCR -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/pdf_to_ocr'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-eye text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">PDF to OCR</h3>
                    <p class="text-gray-600 text-sm">Extract text from scanned PDFs using OCR</p>
                </div>
            </div>

            <!-- Generate QR Code -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/generate_qr'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-gray-500 to-black rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-qrcode text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Generate QR Code</h3>
                    <p class="text-gray-600 text-sm">Create QR codes and convert to PDF</p>
                </div>
            </div>

            <!-- Edit PDF -->
            <div class="tool-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer" onclick="window.location.href='/tool/edit_pdf'">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-edit text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Edit PDF</h3>
                    <p class="text-gray-600 text-sm">Edit text and images in PDF documents</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="bg-gray-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">Why Choose {{ t.site_name }}?</h2>
            <p class="text-xl text-gray-600">Professional PDF processing with advanced features</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-shield-alt text-blue-600 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Secure & Private</h3>
                <p class="text-gray-600">Your files are processed securely and deleted automatically after processing.</p>
            </div>
            
            <div class="text-center">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-bolt text-green-600 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Fast Processing</h3>
                <p class="text-gray-600">Lightning-fast PDF processing with optimized algorithms.</p>
            </div>
            
            <div class="text-center">
                <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-globe text-purple-600 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Multilingual</h3>
                <p class="text-gray-600">Full support for English and Arabic with RTL layout.</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Load more tools functionality
    document.getElementById('loadMoreBtn').addEventListener('click', function() {
        const moreTools = document.getElementById('moreTools');
        const btn = this;
        
        if (moreTools.classList.contains('hidden')) {
            moreTools.classList.remove('hidden');
            btn.textContent = 'Show Less';
        } else {
            moreTools.classList.add('hidden');
            btn.textContent = 'Load More Tools';
        }
    });
</script>
{% endblock %}
