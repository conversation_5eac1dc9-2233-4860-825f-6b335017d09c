{% extends "base.html" %}

{% block title %}GIF to PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-file-pdf text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">GIF to PDF Converter</h1>
        <p class="text-xl text-gray-600">Convert animated GIF files to PDF documents</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a GIF file to convert to PDF</p>
            </div>
            
            <input type="file" id="fileInput" accept=".gif" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                Select GIF File
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- GIF Info -->
    <div id="gifInfo" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">GIF Information</h2>
        <div id="gifDetails" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- GIF details will be populated here -->
        </div>
    </div>
    
    <!-- Conversion Options -->
    <div id="conversionOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Conversion Options</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Layout Options -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Layout Options</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Frame Layout</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="layout" value="separate_pages" checked class="mr-2">
                                <span>Separate Pages</span>
                                <span class="ml-2 text-sm text-gray-500">(Each frame on its own page)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="layout" value="single_page" class="mr-2">
                                <span>Single Page Grid</span>
                                <span class="ml-2 text-sm text-gray-500">(All frames on one page)</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Page Size</label>
                        <select id="pageSize" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500">
                            <option value="A4" selected>A4 (210 × 297 mm)</option>
                            <option value="Letter">Letter (8.5 × 11 in)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Image Quality</label>
                        <select id="quality" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500">
                            <option value="95" selected>High (95%)</option>
                            <option value="85">Medium (85%)</option>
                            <option value="75">Standard (75%)</option>
                            <option value="60">Low (60%)</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Preview -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Preview</h3>
                <div id="gifPreview" class="border-2 border-gray-300 rounded-lg p-4 bg-gray-50 text-center">
                    <p class="text-gray-500">Upload a GIF to see preview</p>
                </div>
                
                <div id="frameInfo" class="mt-4 text-sm text-gray-600 hidden">
                    <p><strong>Frames:</strong> <span id="frameCount">0</span></p>
                    <p><strong>Duration:</strong> <span id="totalDuration">0</span>ms</p>
                    <p><strong>Dimensions:</strong> <span id="dimensions">0×0</span></p>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview Layout
            </button>
            <button id="convertBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-file-pdf mr-2"></i>Convert to PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-pink-200 border-t-pink-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Converting GIF frames to PDF...</p>
            
            <div class="w-full bg-gray-200 rounded-full h-2 mt-6">
                <div id="progressBar" class="bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your GIF has been successfully converted to PDF!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newConversionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Convert Another GIF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-pink-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-pink-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-pink-700">
            <li>Upload your animated GIF file</li>
            <li>Review the GIF information (frames, duration, size)</li>
            <li>Choose layout: separate pages or single page grid</li>
            <li>Select page size and image quality</li>
            <li>Preview the layout if needed</li>
            <li>Convert to PDF and download your file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb mr-2"></i>
                <strong>Tip:</strong> Use "Separate Pages" for flipbook-style PDFs where each frame is a page. 
                Use "Single Page Grid" to see all frames at once on one page.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let gifInfo = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'image/gif') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewLayout);
        document.getElementById('convertBtn').addEventListener('click', convertToPDF);
        document.getElementById('newConversionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'image/gif') {
            selectedFile = file;
            displayFileInfo(file);
            loadGifInfo();
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-image text-pink-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function loadGifInfo() {
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        fetch('/api/get_gif_info', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            gifInfo = data;
            displayGifInfo(data);
            displayGifPreview();
            document.getElementById('conversionOptions').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading GIF info:', error);
            // Show basic info even if API fails
            displayBasicGifInfo();
            document.getElementById('conversionOptions').classList.remove('hidden');
        });
    }
    
    function displayGifInfo(info) {
        const gifInfoSection = document.getElementById('gifInfo');
        const gifDetails = document.getElementById('gifDetails');
        
        gifDetails.innerHTML = `
            <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">${info.frame_count || 'N/A'}</div>
                <div class="text-gray-600 text-sm">Frames</div>
            </div>
            <div class="bg-green-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600">${info.width}×${info.height}</div>
                <div class="text-gray-600 text-sm">Dimensions</div>
            </div>
            <div class="bg-purple-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-purple-600">${(info.total_duration / 1000).toFixed(1)}s</div>
                <div class="text-gray-600 text-sm">Duration</div>
            </div>
            <div class="bg-orange-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-orange-600">${info.is_animated ? 'Yes' : 'No'}</div>
                <div class="text-gray-600 text-sm">Animated</div>
            </div>
        `;
        
        gifInfoSection.classList.remove('hidden');
        
        // Update frame info
        document.getElementById('frameCount').textContent = info.frame_count || 0;
        document.getElementById('totalDuration').textContent = info.total_duration || 0;
        document.getElementById('dimensions').textContent = `${info.width}×${info.height}`;
        document.getElementById('frameInfo').classList.remove('hidden');
    }
    
    function displayBasicGifInfo() {
        const gifInfoSection = document.getElementById('gifInfo');
        const gifDetails = document.getElementById('gifDetails');
        
        gifDetails.innerHTML = `
            <div class="col-span-full bg-gray-50 rounded-lg p-4 text-center">
                <p class="text-gray-600">GIF information will be analyzed during conversion</p>
            </div>
        `;
        
        gifInfoSection.classList.remove('hidden');
    }
    
    function displayGifPreview() {
        const preview = document.getElementById('gifPreview');
        
        if (selectedFile) {
            const url = URL.createObjectURL(selectedFile);
            preview.innerHTML = `
                <img src="${url}" alt="GIF Preview" class="max-w-full max-h-48 mx-auto rounded">
                <p class="text-sm text-gray-600 mt-2">Original GIF Animation</p>
            `;
        }
    }
    
    function previewLayout() {
        const layout = document.querySelector('input[name="layout"]:checked').value;
        const pageSize = document.getElementById('pageSize').value;
        
        if (layout === 'separate_pages') {
            alert(`Preview: Each GIF frame will be placed on a separate ${pageSize} page, maintaining aspect ratio and centering the image.`);
        } else {
            alert(`Preview: All GIF frames will be arranged in a grid layout on a single ${pageSize} page.`);
        }
    }
    
    function convertToPDF() {
        if (!selectedFile) {
            alert('Please select a GIF file first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('layout', document.querySelector('input[name="layout"]:checked').value);
        formData.append('page_size', document.getElementById('pageSize').value);
        formData.append('quality', document.getElementById('quality').value);
        
        // Show progress
        document.getElementById('conversionOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        // Simulate progress
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);
        
        fetch('/api/gif_to_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Conversion failed');
            }
        })
        .then(blob => {
            setTimeout(() => {
                document.getElementById('progressSection').classList.add('hidden');
                document.getElementById('resultSection').classList.remove('hidden');
                
                // Setup download
                const url = window.URL.createObjectURL(blob);
                document.getElementById('downloadBtn').onclick = function() {
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'gif_to_pdf.pdf';
                    a.click();
                };
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            alert('Error converting GIF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('conversionOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        gifInfo = null;
        
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('gifInfo').classList.add('hidden');
        document.getElementById('conversionOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        document.getElementById('frameInfo').classList.add('hidden');
        
        // Reset preview
        document.getElementById('gifPreview').innerHTML = '<p class="text-gray-500">Upload a GIF to see preview</p>';
        
        // Reset form values
        document.querySelector('input[name="layout"][value="separate_pages"]').checked = true;
        document.getElementById('pageSize').value = 'A4';
        document.getElementById('quality').value = '95';
    }
</script>
{% endblock %}
