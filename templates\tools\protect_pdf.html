{% extends "base.html" %}

{% block title %}Protect PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-lock text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Protect PDF</h1>
        <p class="text-xl text-gray-600">Add password protection to secure your PDF files</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to add password protection</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Protection Options -->
    <div id="protectionOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Password Protection Settings</h2>
        
        <div class="space-y-6">
            <!-- User Password -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">User Password (Required to open PDF)</label>
                <input type="password" id="userPassword" placeholder="Enter password to open the PDF" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                <p class="text-sm text-gray-500 mt-1">Users will need this password to open and view the PDF</p>
            </div>
            
            <!-- Owner Password -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Owner Password (Optional - for editing permissions)</label>
                <input type="password" id="ownerPassword" placeholder="Enter password for editing permissions" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                <p class="text-sm text-gray-500 mt-1">This password controls what users can do with the PDF</p>
            </div>
            
            <!-- Permissions -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Permissions (when owner password is set)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="allowPrinting" checked class="mr-2">
                        Allow Printing
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="allowCopying" class="mr-2">
                        Allow Copying Text
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="allowModifying" class="mr-2">
                        Allow Modifying
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="allowAnnotations" checked class="mr-2">
                        Allow Annotations
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="allowForms" checked class="mr-2">
                        Allow Form Filling
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="allowAssembly" class="mr-2">
                        Allow Document Assembly
                    </label>
                </div>
            </div>
            
            <!-- Encryption Level -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Encryption Level</label>
                <select id="encryptionLevel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                    <option value="128">128-bit (Recommended)</option>
                    <option value="256">256-bit (High Security)</option>
                </select>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button id="generatePasswordBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-key mr-2"></i>Generate Strong Password
            </button>
            <button id="protectBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-lock mr-2"></i>Protect PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-red-200 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Adding password protection to your PDF...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF has been successfully protected with a password!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newProtectionBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Protect Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-red-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-red-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-red-700">
            <li>Upload your PDF file</li>
            <li>Set a user password (required to open the PDF)</li>
            <li>Optionally set an owner password for editing permissions</li>
            <li>Choose which actions to allow or restrict</li>
            <li>Select encryption level for security</li>
            <li>Protect your PDF and download the secured file</li>
        </ol>
        
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-yellow-800 text-sm">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <strong>Important:</strong> Remember your passwords! If you forget them, you won't be able to open or modify the PDF.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Buttons
        document.getElementById('generatePasswordBtn').addEventListener('click', generatePassword);
        document.getElementById('protectBtn').addEventListener('click', protectPDF);
        document.getElementById('newProtectionBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('protectionOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function generatePassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let password = '';
        for (let i = 0; i < 12; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        document.getElementById('userPassword').value = password;
        
        // Show password in alert for user to copy
        alert(`Generated strong password: ${password}\n\nPlease copy and save this password securely!`);
    }
    
    function protectPDF() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const userPassword = document.getElementById('userPassword').value;
        if (!userPassword.trim()) {
            alert('Please enter a user password.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('user_password', userPassword);
        
        const ownerPassword = document.getElementById('ownerPassword').value;
        if (ownerPassword.trim()) {
            formData.append('owner_password', ownerPassword);
        }
        
        // Permissions
        formData.append('allow_printing', document.getElementById('allowPrinting').checked);
        formData.append('allow_copying', document.getElementById('allowCopying').checked);
        formData.append('allow_modifying', document.getElementById('allowModifying').checked);
        formData.append('allow_annotations', document.getElementById('allowAnnotations').checked);
        formData.append('allow_forms', document.getElementById('allowForms').checked);
        formData.append('allow_assembly', document.getElementById('allowAssembly').checked);
        formData.append('encryption_level', document.getElementById('encryptionLevel').value);
        
        // Show progress
        document.getElementById('protectionOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/protect_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Protection failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'protected.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error protecting PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('protectionOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('protectionOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset form values
        document.getElementById('userPassword').value = '';
        document.getElementById('ownerPassword').value = '';
        document.getElementById('allowPrinting').checked = true;
        document.getElementById('allowCopying').checked = false;
        document.getElementById('allowModifying').checked = false;
        document.getElementById('allowAnnotations').checked = true;
        document.getElementById('allowForms').checked = true;
        document.getElementById('allowAssembly').checked = false;
        document.getElementById('encryptionLevel').value = '128';
    }
</script>
{% endblock %}
