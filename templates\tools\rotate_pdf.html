{% extends "base.html" %}

{% block title %}Rotate PDF - {{ t.site_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-redo text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Rotate PDF Pages</h1>
        <p class="text-xl text-gray-600">Rotate PDF pages to the correct orientation</p>
    </div>
    
    <!-- Upload Area -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-300">
            <div class="mb-6">
                <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ t.upload.drag_drop }}</h3>
                <p class="text-gray-500">Select a PDF file to rotate its pages</p>
            </div>
            
            <input type="file" id="fileInput" accept=".pdf" class="hidden">
            <button onclick="document.getElementById('fileInput').click()" class="btn-primary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                {{ t.upload.select_files }}
            </button>
        </div>
        
        <!-- File Info -->
        <div id="fileInfo" class="mt-8 hidden">
            <div id="fileDetails" class="bg-gray-50 rounded-lg p-4"></div>
        </div>
    </div>
    
    <!-- Rotation Options -->
    <div id="rotationOptions" class="bg-white rounded-xl shadow-lg p-8 mb-8 hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Rotation Settings</h2>
        
        <!-- Rotation Direction -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="rotation-option border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-blue-300 transition-colors text-center" data-angle="90">
                <i class="fas fa-redo text-3xl text-blue-500 mb-3"></i>
                <h3 class="font-semibold text-gray-800">90° Right</h3>
                <p class="text-sm text-gray-600">Clockwise</p>
            </div>
            
            <div class="rotation-option border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-blue-300 transition-colors text-center" data-angle="180">
                <i class="fas fa-sync text-3xl text-green-500 mb-3"></i>
                <h3 class="font-semibold text-gray-800">180°</h3>
                <p class="text-sm text-gray-600">Upside Down</p>
            </div>
            
            <div class="rotation-option border-2 border-blue-500 bg-blue-50 rounded-lg p-6 cursor-pointer text-center" data-angle="270">
                <i class="fas fa-undo text-3xl text-orange-500 mb-3"></i>
                <h3 class="font-semibold text-gray-800">90° Left</h3>
                <p class="text-sm text-gray-600">Counter-clockwise</p>
            </div>
            
            <div class="rotation-option border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-blue-300 transition-colors text-center" data-angle="0">
                <i class="fas fa-file text-3xl text-gray-500 mb-3"></i>
                <h3 class="font-semibold text-gray-800">No Rotation</h3>
                <p class="text-sm text-gray-600">Original</p>
            </div>
        </div>
        
        <!-- Page Selection -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Apply to Pages</label>
            <div class="space-y-3">
                <label class="flex items-center">
                    <input type="radio" name="pageSelection" value="all" checked class="mr-2">
                    All pages
                </label>
                <label class="flex items-center">
                    <input type="radio" name="pageSelection" value="specific" class="mr-2">
                    Specific pages
                </label>
                <label class="flex items-center">
                    <input type="radio" name="pageSelection" value="odd" class="mr-2">
                    Odd pages only
                </label>
                <label class="flex items-center">
                    <input type="radio" name="pageSelection" value="even" class="mr-2">
                    Even pages only
                </label>
            </div>
            
            <div id="specificPagesInput" class="mt-3 hidden">
                <input type="text" id="specificPages" placeholder="e.g., 1,3,5-10" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-sm text-gray-500 mt-1">Enter page numbers separated by commas</p>
            </div>
        </div>
        
        <!-- Preview -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Rotation Preview</h3>
            <div class="flex items-center justify-center space-x-8">
                <div class="text-center">
                    <div class="w-16 h-20 bg-white border-2 border-gray-300 rounded mb-2 flex items-center justify-center">
                        <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-sm text-gray-600">Original</p>
                </div>
                
                <i class="fas fa-arrow-right text-gray-400 text-2xl"></i>
                
                <div class="text-center">
                    <div id="previewRotation" class="w-16 h-20 bg-white border-2 border-blue-300 rounded mb-2 flex items-center justify-center transform rotate-90">
                        <i class="fas fa-file-alt text-blue-500 text-2xl"></i>
                    </div>
                    <p class="text-sm text-gray-600">After Rotation</p>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4">
            <button id="previewBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-eye mr-2"></i>Preview
            </button>
            <button id="rotateBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                <i class="fas fa-redo mr-2"></i>Rotate PDF
            </button>
        </div>
    </div>
    
    <!-- Progress -->
    <div id="progressSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing }}</h3>
            <p class="text-gray-600">Rotating your PDF pages...</p>
        </div>
    </div>
    
    <!-- Result -->
    <div id="resultSection" class="bg-white rounded-xl shadow-lg p-8 hidden">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ t.upload.processing_complete }}</h3>
            <p class="text-gray-600 mb-6">Your PDF pages have been successfully rotated!</p>
            
            <div class="flex justify-center space-x-4">
                <button id="downloadBtn" class="btn-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-download mr-2"></i>{{ t.upload.download }}
                </button>
                <button id="newRotationBtn" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Rotate Another PDF
                </button>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="bg-blue-50 rounded-xl p-6 mt-8">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            How to use:
        </h3>
        <ol class="list-decimal list-inside space-y-2 text-blue-700">
            <li>Upload your PDF file</li>
            <li>Select rotation angle (90° right, 180°, 90° left, or no rotation)</li>
            <li>Choose which pages to rotate (all, specific, odd, or even)</li>
            <li>Preview the rotation effect</li>
            <li>Apply rotation and download your corrected PDF</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedFile = null;
    let selectedAngle = 270; // Default to 90° left
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // Rotation selection
        document.querySelectorAll('.rotation-option').forEach(option => {
            option.addEventListener('click', function() {
                selectRotation(parseInt(this.dataset.angle));
            });
        });
        
        // Page selection
        document.querySelectorAll('input[name="pageSelection"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const specificInput = document.getElementById('specificPagesInput');
                if (this.value === 'specific') {
                    specificInput.classList.remove('hidden');
                } else {
                    specificInput.classList.add('hidden');
                }
            });
        });
        
        // Buttons
        document.getElementById('previewBtn').addEventListener('click', previewRotation);
        document.getElementById('rotateBtn').addEventListener('click', rotatePDF);
        document.getElementById('newRotationBtn').addEventListener('click', resetForm);
    }
    
    function handleFileSelect() {
        const file = document.getElementById('fileInput').files[0];
        if (file && file.type === 'application/pdf') {
            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('rotationOptions').classList.remove('hidden');
        }
    }
    
    function displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        fileDetails.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-2xl mr-4"></i>
                <div>
                    <p class="font-medium text-gray-800">${file.name}</p>
                    <p class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
            </div>
        `;
        
        fileInfo.classList.remove('hidden');
    }
    
    function selectRotation(angle) {
        selectedAngle = angle;
        
        // Update UI
        document.querySelectorAll('.rotation-option').forEach(option => {
            option.classList.remove('border-blue-500', 'bg-blue-50');
            option.classList.add('border-gray-200');
        });
        
        const selectedOption = document.querySelector(`[data-angle="${angle}"]`);
        selectedOption.classList.remove('border-gray-200');
        selectedOption.classList.add('border-blue-500', 'bg-blue-50');
        
        // Update preview
        updatePreview(angle);
    }
    
    function updatePreview(angle) {
        const preview = document.getElementById('previewRotation');
        preview.className = 'w-16 h-20 bg-white border-2 border-blue-300 rounded mb-2 flex items-center justify-center transform';
        
        switch (angle) {
            case 90:
                preview.classList.add('rotate-90');
                break;
            case 180:
                preview.classList.add('rotate-180');
                break;
            case 270:
                preview.classList.add('-rotate-90');
                break;
            default:
                // No rotation
                break;
        }
    }
    
    function previewRotation() {
        const pageSelection = document.querySelector('input[name="pageSelection"]:checked').value;
        let pageText = '';
        
        switch (pageSelection) {
            case 'all':
                pageText = 'all pages';
                break;
            case 'specific':
                const specificPages = document.getElementById('specificPages').value;
                if (!specificPages.trim()) {
                    alert('Please specify which pages to rotate.');
                    return;
                }
                pageText = `pages: ${specificPages}`;
                break;
            case 'odd':
                pageText = 'odd pages only';
                break;
            case 'even':
                pageText = 'even pages only';
                break;
        }
        
        const angleText = selectedAngle === 0 ? 'no rotation' : `${selectedAngle}° rotation`;
        
        alert(`Preview: ${angleText} will be applied to ${pageText}.`);
    }
    
    function rotatePDF() {
        if (!selectedFile) {
            alert('Please select a PDF file first.');
            return;
        }
        
        const pageSelection = document.querySelector('input[name="pageSelection"]:checked').value;
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('rotation_angle', selectedAngle);
        formData.append('page_selection', pageSelection);
        
        if (pageSelection === 'specific') {
            const specificPages = document.getElementById('specificPages').value;
            if (!specificPages.trim()) {
                alert('Please specify which pages to rotate.');
                return;
            }
            formData.append('specific_pages', specificPages);
        }
        
        // Show progress
        document.getElementById('rotationOptions').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');
        
        fetch('/api/rotate_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                throw new Error('Rotation failed');
            }
        })
        .then(blob => {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultSection').classList.remove('hidden');
            
            // Setup download
            const url = window.URL.createObjectURL(blob);
            document.getElementById('downloadBtn').onclick = function() {
                const a = document.createElement('a');
                a.href = url;
                a.download = 'rotated.pdf';
                a.click();
            };
        })
        .catch(error => {
            alert('Error rotating PDF: ' + error.message);
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('rotationOptions').classList.remove('hidden');
        });
    }
    
    function resetForm() {
        selectedFile = null;
        selectedAngle = 270;
        
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('rotationOptions').classList.add('hidden');
        document.getElementById('resultSection').classList.add('hidden');
        
        // Reset rotation selection
        selectRotation(270);
        
        // Reset page selection
        document.querySelector('input[name="pageSelection"][value="all"]').checked = true;
        document.getElementById('specificPagesInput').classList.add('hidden');
        document.getElementById('specificPages').value = '';
    }
</script>
{% endblock %}
